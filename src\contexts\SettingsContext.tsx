'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { getPastelColorScheme } from '@/utils/pastelColorSchemes';
import { ColorScheme } from '@/types/settings';

interface Settings {
  colorScheme: ColorScheme;
  darkMode: boolean;
  fontSize: string;
}

interface SettingsContextType {
  settings: Settings;
  updateSettings: (newSettings: Partial<Settings>) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

const defaultSettings: Settings = {
  colorScheme: 'default',
  darkMode: false,
  fontSize: 'medium',
};

const SettingsContext = createContext<SettingsContextType>({
  settings: defaultSettings,
  updateSettings: async () => {},
  isLoading: false,
  error: null,
});

export const useSettings = () => useContext(SettingsContext);

export const SettingsProvider = ({ children }: { children: ReactNode }) => {
  const { data: session, status } = useSession();
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch settings when session is ready
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Wait for session to be determined
        if (status === 'loading') {
          return; // Still loading session, wait
        }

        // If no session, use default settings
        if (status === 'unauthenticated' || !session) {
          console.log('No session found, using default settings');
          setSettings(defaultSettings);
          setIsLoading(false);
          return;
        }

        console.log('Session found, fetching user settings for:', session.user?.name);

        const response = await fetch('/api/settings');

        if (!response.ok) {
          // If unauthorized (401), just use default settings instead of throwing error
          if (response.status === 401) {
            console.warn('User not authenticated, using default settings');
            setSettings(defaultSettings);
            return;
          }
          throw new Error('Failed to fetch settings');
        }

        const data = await response.json();

        if (data.success && data.settings) {
          setSettings({
            colorScheme: data.settings.colorScheme,
            darkMode: data.settings.darkMode,
            fontSize: data.settings.fontSize,
          });
        } else {
          // If no settings found, use defaults
          setSettings(defaultSettings);
        }
      } catch (err: any) {
        console.error('Error fetching settings:', err);
        // Use default settings instead of showing error
        setSettings(defaultSettings);
        setError(null); // Don't show error to user, just use defaults
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [session, status]); // Depend on session and status

  // Update settings
  const updateSettings = async (newSettings: Partial<Settings>) => {
    try {
      setIsLoading(true);
      setError(null);

      // Update local state immediately for better UX
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);

      // If no session, just keep local settings
      if (status === 'unauthenticated' || !session) {
        console.log('No session, settings saved locally only');
        setIsLoading(false);
        return;
      }

      // Save to server
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      });

      if (!response.ok) {
        // If unauthorized, just keep the local settings without throwing error
        if (response.status === 401) {
          console.warn('User not authenticated, settings saved locally only');
          return;
        }
        throw new Error('Failed to update settings');
      }

      // Apply settings in the correct order to ensure proper theme application

      // First, remove all theme classes regardless of what's changing
      const themeClasses = [
        'theme-default',
        'theme-lavender',
        'theme-mint',
        'theme-peach',
        'theme-skyBlue',
        'theme-coral',
        'theme-lemon',
        'theme-periwinkle',
        'theme-rose'
      ];
      document.documentElement.classList.remove(...themeClasses);

      // Special case: if selecting default color scheme, ensure dark mode is off
      if (newSettings.colorScheme === 'default') {
        document.documentElement.classList.remove('dark');
        // Update the darkMode setting to false if it's not already
        if (newSettings.darkMode === true) {
          newSettings.darkMode = false;
          // Also update localStorage to keep it in sync
          localStorage.setItem('darkMode', 'false');
        }
      }
      // Otherwise, apply dark mode as requested
      else if (newSettings.darkMode !== undefined) {
        if (newSettings.darkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }

      // Finally apply color scheme (after dark mode)
      // This ensures the color scheme's variables are applied properly
      if (newSettings.colorScheme && newSettings.colorScheme !== 'default') {
        document.documentElement.classList.add(`theme-${newSettings.colorScheme}`);
      }

      // Apply font size
      if (newSettings.fontSize) {
        document.documentElement.classList.remove('text-sm', 'text-base', 'text-lg');
        switch (newSettings.fontSize) {
          case 'small':
            document.documentElement.classList.add('text-sm');
            break;
          case 'medium':
            document.documentElement.classList.add('text-base');
            break;
          case 'large':
            document.documentElement.classList.add('text-lg');
            break;
        }
      }

      // Log changes
      if (newSettings.colorScheme || newSettings.darkMode !== undefined) {
        console.log(`Applied settings - Color scheme: ${newSettings.colorScheme || settings.colorScheme}, Dark mode: ${newSettings.darkMode !== undefined ? newSettings.darkMode : settings.darkMode}`);
      }

    } catch (err: any) {
      console.error('Error updating settings:', err);
      setError(err.message || 'An error occurred while updating settings');

      // Revert to previous settings on error
      setSettings(settings);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply settings on initial load
  useEffect(() => {
    // Apply settings in the correct order to ensure proper theme application

    // First, remove all theme classes
    const themeClasses = [
      'theme-default',
      'theme-lavender',
      'theme-mint',
      'theme-peach',
      'theme-skyBlue',
      'theme-coral',
      'theme-lemon',
      'theme-periwinkle',
      'theme-rose'
    ];
    document.documentElement.classList.remove(...themeClasses);

    // Special case: if using default color scheme, ensure dark mode is off
    if (settings.colorScheme === 'default') {
      document.documentElement.classList.remove('dark');
      // If settings has dark mode on but color scheme is default, update settings
      if (settings.darkMode) {
        setSettings(prev => ({
          ...prev,
          darkMode: false
        }));
        // Also update localStorage to keep it in sync
        localStorage.setItem('darkMode', 'false');
      }
    }
    // Otherwise, apply dark mode as requested
    else if (settings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Finally apply color scheme (after dark mode)
    // This ensures the color scheme's variables are applied properly
    if (settings.colorScheme !== 'default') {
      document.documentElement.classList.add(`theme-${settings.colorScheme}`);
    }

    // Apply font size
    document.documentElement.classList.remove('text-sm', 'text-base', 'text-lg');
    switch (settings.fontSize) {
      case 'small':
        document.documentElement.classList.add('text-sm');
        break;
      case 'medium':
        document.documentElement.classList.add('text-base');
        break;
      case 'large':
        document.documentElement.classList.add('text-lg');
        break;
    }

    console.log(`Applied color scheme: ${settings.colorScheme}, Dark mode: ${settings.darkMode}`);
  }, [settings]);

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, isLoading, error }}>
      {children}
    </SettingsContext.Provider>
  );
};
