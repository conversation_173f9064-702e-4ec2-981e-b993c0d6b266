import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';

// Mark this route as dynamic to prevent static generation
export const dynamic = 'force-dynamic';

// GET /api/test-session - Test session data
export async function GET() {
  try {
    console.log('=== TEST SESSION API CALLED ===');
    
    // Get the session
    const session = await getServerSession(authOptions);
    
    console.log('Raw session from getServerSession:', JSON.stringify(session, null, 2));
    
    if (!session) {
      console.log('No session found');
      return NextResponse.json({
        success: false,
        message: 'No session found',
        session: null,
      });
    }
    
    if (!session.user) {
      console.log('Session exists but no user data');
      return NextResponse.json({
        success: false,
        message: 'Session exists but no user data',
        session: session,
      });
    }
    
    console.log('Session user data:', {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      role: session.user.role,
      division: session.user.division,
    });
    
    return NextResponse.json({
      success: true,
      message: 'Session found',
      session: {
        user: {
          id: session.user.id,
          name: session.user.name,
          email: session.user.email,
          role: session.user.role,
          division: session.user.division,
        },
        serverTimestamp: session.serverTimestamp,
        sessionToken: session.sessionToken,
      },
      debug: {
        hasId: !!session.user.id,
        idType: typeof session.user.id,
        idValue: session.user.id,
      }
    });
    
  } catch (error: any) {
    console.error('Error in test-session API:', error);
    return NextResponse.json({
      success: false,
      message: 'Error testing session',
      error: error.message,
    }, { status: 500 });
  }
}
