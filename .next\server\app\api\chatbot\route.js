"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Documents_Euf_Coding_DocumentTracker_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chatbot/route.ts */ \"(rsc)/./src/app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Documents_Euf_Coding_DocumentTracker_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chatbot/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chatbot/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/chatbot/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/options */ \"(rsc)/./src/lib/auth/options.ts\");\n/* harmony import */ var _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/chatbot/chatbotService */ \"(rsc)/./src/lib/chatbot/chatbotService.ts\");\n\n\n\n\n// POST /api/chatbot - Start session or send message\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { action, sessionId, message, context } = body;\n        switch(action){\n            case \"start_session\":\n                const newSession = await _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.startSession(session.user.id, {\n                    user: session.user,\n                    currentPage: context?.currentPage,\n                    systemState: context?.systemState\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Chat session started\",\n                    data: {\n                        sessionId: newSession.id,\n                        messages: newSession.messages,\n                        context: newSession.context\n                    }\n                });\n            case \"send_message\":\n                if (!sessionId || !message) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Session ID and message are required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                try {\n                    const response = await _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.sendMessage(sessionId, message);\n                    const updatedSession = _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.getSession(sessionId);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Message sent successfully\",\n                        data: {\n                            response,\n                            sessionId,\n                            messages: updatedSession?.messages || []\n                        }\n                    });\n                } catch (messageError) {\n                    console.error(\"Error in send_message:\", messageError);\n                    // If session was not found, suggest creating a new one\n                    if (messageError.message.includes(\"Chat session not found\")) {\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            message: \"Chat session expired. Please refresh the page to start a new conversation.\",\n                            error: \"SESSION_EXPIRED\",\n                            suggestion: \"refresh_page\"\n                        }, {\n                            status: 410\n                        } // Gone - indicates the session has expired\n                        );\n                    }\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: `Failed to send message: ${messageError.message}`\n                    }, {\n                        status: 500\n                    });\n                }\n            case \"update_context\":\n                if (!sessionId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Session ID is required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.updateSessionContext(sessionId, context);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Context updated successfully\"\n                });\n            case \"update_document_context\":\n                if (!sessionId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Session ID is required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const { documentData } = body;\n                _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.updateDocumentContext(sessionId, documentData);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Document context updated successfully\"\n                });\n            case \"get_contextual_help\":\n                const help = await _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.getContextualHelp(session.user.id, {\n                    user: session.user,\n                    currentPage: context?.currentPage,\n                    recentActions: context?.recentActions,\n                    pendingDocuments: context?.pendingDocuments,\n                    commonIssues: context?.commonIssues\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Contextual help generated\",\n                    data: help\n                });\n            case \"analyze_intent\":\n                if (!message) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Message is required for intent analysis\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const intent = await _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.analyzeIntent(message, {\n                    user: session.user,\n                    currentPage: context?.currentPage\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Intent analyzed successfully\",\n                    data: intent\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Invalid action. Use: start_session, send_message, update_context, update_document_context, get_contextual_help, or analyze_intent\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in chatbot API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: error.message || \"Something went wrong\"\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/chatbot - Get session info or stats\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get(\"action\");\n        const sessionId = searchParams.get(\"sessionId\");\n        switch(action){\n            case \"get_session\":\n                if (!sessionId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Session ID is required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const chatSession = _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.getSession(sessionId);\n                if (!chatSession) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Session not found\"\n                    }, {\n                        status: 404\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Session retrieved successfully\",\n                    data: chatSession\n                });\n            case \"get_stats\":\n                // Only admins can view stats\n                if (session.user.role !== \"ADMIN\" && session.user.role !== \"REGIONAL_DIRECTOR\") {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        message: \"Forbidden\"\n                    }, {\n                        status: 403\n                    });\n                }\n                const stats = _lib_chatbot_chatbotService__WEBPACK_IMPORTED_MODULE_3__.chatbotService.getStats();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Stats retrieved successfully\",\n                    data: stats\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Invalid action. Use: get_session or get_stats\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in chatbot GET API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: error.message || \"Something went wrong\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chatbot/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth/options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _utils_audit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/audit */ \"(rsc)/./src/utils/audit.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/serverTimestamp */ \"(rsc)/./src/utils/serverTimestamp.ts\");\n/* harmony import */ var _utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/sessionToken */ \"(rsc)/./src/utils/sessionToken.ts\");\n\n\n\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            // Define the credentials that will be submitted from the login form\n            credentials: {\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                // Add more detailed logging of received credentials\n                console.log(\"Received credentials:\", credentials);\n                if (!credentials?.name || !credentials?.password) {\n                    console.error(\"Missing credentials:\", {\n                        name: !!credentials?.name,\n                        password: !!credentials?.password\n                    });\n                    throw new Error(\"Name and password required\");\n                }\n                try {\n                    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                    console.log(\"Looking up user with name:\", credentials.name);\n                    // Make the search case-insensitive and escape special characters in the regex\n                    const escapedName = credentials.name.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                        name: {\n                            $regex: new RegExp(`^${escapedName}$`, \"i\")\n                        }\n                    }).select(\"+password\");\n                    if (!user) {\n                        console.log(\"User not found with name:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"User found, checking password\");\n                    if (!user.password) {\n                        console.log(\"User has no password set\");\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    const isPasswordCorrect = await (0,bcrypt__WEBPACK_IMPORTED_MODULE_1__.compare)(credentials.password, user.password);\n                    if (!isPasswordCorrect) {\n                        console.log(\"Password incorrect for user:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"Authentication successful for user:\", credentials.name);\n                    const userId = user._id?.toString() || \"\";\n                    // Create a new session token\n                    const userAgent = req?.headers?.[\"user-agent\"];\n                    const ipAddress = req?.headers?.[\"x-forwarded-for\"] || req?.socket?.remoteAddress || \"unknown\";\n                    const sessionToken = await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.createSession)(userId, userAgent, ipAddress);\n                    // Log successful login\n                    await (0,_utils_audit__WEBPACK_IMPORTED_MODULE_4__.logAuditEvent)({\n                        action: _types_audit__WEBPACK_IMPORTED_MODULE_5__.AuditLogAction.USER_LOGIN,\n                        performedBy: userId,\n                        targetId: userId,\n                        targetType: \"User\",\n                        details: {\n                            name: user.name,\n                            email: user.email,\n                            role: user.role,\n                            division: user.division,\n                            sessionToken: sessionToken\n                        }\n                    });\n                    return {\n                        id: userId,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role,\n                        division: user.division,\n                        image: user.image,\n                        sessionToken: sessionToken\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw new Error(\"Authentication failed. Please try again.\");\n                }\n                // This code is unreachable due to the try/catch block above\n                // but we'll keep it as a fallback\n                console.error(\"Warning: Reached unreachable code in NextAuth authorize callback\");\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger }) {\n            console.log(\"JWT callback called with user:\", user);\n            console.log(\"Initial token:\", token);\n            if (user) {\n                // Add user data to token\n                token.id = user.id;\n                // Cast user to any to access custom properties\n                const customUser = user;\n                token.name = customUser.name; // Ensure name is included\n                token.role = customUser.role;\n                token.division = customUser.division;\n                // Add session token to JWT\n                if (customUser.sessionToken) {\n                    token.sessionToken = customUser.sessionToken;\n                }\n                // Add server timestamp to token to invalidate sessions on server restart\n                token.serverTimestamp = (0,_utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__.getServerTimestamp)();\n                console.log(\"Updated token with user data:\", token);\n            }\n            // Handle sign out\n            if (trigger === \"signOut\") {\n                // Remove the session from the database when the user signs out\n                if (token.id && token.sessionToken) {\n                    await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.removeSession)(token.id, token.sessionToken);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"Session callback called with token:\", token);\n            console.log(\"Initial session:\", session);\n            if (token) {\n                // Add user data to session\n                session.user.id = token.id;\n                session.user.name = token.name; // Ensure name is included\n                session.user.role = token.role;\n                session.user.division = token.division;\n                // Add server timestamp to session\n                session.serverTimestamp = token.serverTimestamp;\n                // Add session token to session\n                if (token.sessionToken) {\n                    session.sessionToken = token.sessionToken;\n                }\n            }\n            console.log(\"Returning session:\", session);\n            return session;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/chatbot/chatbotService.ts":
/*!*******************************************!*\
  !*** ./src/lib/chatbot/chatbotService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatbotService: () => (/* binding */ chatbotService)\n/* harmony export */ });\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini/index.ts\");\n/**\n * Chatbot Service\n * Manages conversation state and integrates with Gemini AI\n */ \nclass ChatbotService {\n    /**\n   * Start a new chat session\n   */ async startSession(userId, userContext) {\n        const sessionId = `chat_${userId}_${Date.now()}`;\n        const session = {\n            id: sessionId,\n            userId,\n            messages: [],\n            context: {\n                user: userContext.user,\n                currentPage: userContext.currentPage,\n                systemState: userContext.systemState\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        // Add welcome message\n        const welcomeMessage = await this.generateWelcomeMessage(userContext);\n        session.messages.push({\n            id: `msg_${Date.now()}`,\n            role: \"assistant\",\n            content: welcomeMessage.message,\n            timestamp: new Date(),\n            actions: welcomeMessage.actions,\n            quickReplies: welcomeMessage.quickReplies\n        });\n        this.sessions.set(sessionId, session);\n        return session;\n    }\n    /**\n   * Send a message and get response\n   */ async sendMessage(sessionId, userMessage) {\n        let session = this.sessions.get(sessionId);\n        // If session not found, try to recover or create a new one\n        if (!session) {\n            console.warn(`Chat session ${sessionId} not found, attempting to recover...`);\n            // Extract userId from sessionId if possible\n            const userIdMatch = sessionId.match(/^chat_(.+)_\\d+$/);\n            if (userIdMatch) {\n                const userId = userIdMatch[1];\n                console.log(`Creating new session for user ${userId}`);\n                // Create a minimal session to continue the conversation\n                session = {\n                    id: sessionId,\n                    userId,\n                    messages: [],\n                    context: {\n                        user: {\n                            id: userId\n                        },\n                        currentPage: \"/documents\",\n                        systemState: {\n                            timestamp: new Date().toISOString()\n                        }\n                    },\n                    createdAt: new Date(),\n                    updatedAt: new Date()\n                };\n                // Add a recovery message\n                const recoveryMessage = {\n                    id: `msg_${Date.now()}_recovery`,\n                    role: \"assistant\",\n                    content: `👋 **Welcome back!** I noticed our conversation was interrupted, but I'm here to help you with the Document Tracking System.\n\n**🔄 Session Recovered** - You can continue asking questions about:\n• Document management and workflows\n• System navigation and features\n• Routing slips and document journeys\n• Troubleshooting and support\n\nHow can I assist you today?`,\n                    timestamp: new Date(),\n                    actions: [\n                        {\n                            type: \"navigate\",\n                            label: \"\\uD83D\\uDCCA Dashboard\",\n                            data: {\n                                url: \"/dashboard\"\n                            }\n                        },\n                        {\n                            type: \"navigate\",\n                            label: \"\\uD83D\\uDCDD Create Document\",\n                            data: {\n                                url: \"/documents/add\"\n                            }\n                        },\n                        {\n                            type: \"help\",\n                            label: \"\\uD83D\\uDCDA Help Guide\",\n                            data: {\n                                topic: \"help\"\n                            }\n                        }\n                    ],\n                    quickReplies: [\n                        \"How to send documents?\",\n                        \"Check my inbox\",\n                        \"Document workflow\",\n                        \"System features\"\n                    ]\n                };\n                session.messages.push(recoveryMessage);\n                this.sessions.set(sessionId, session);\n            } else {\n                throw new Error(\"Chat session not found and cannot be recovered. Please refresh the page to start a new session.\");\n            }\n        }\n        // Add user message to session\n        const userMsg = {\n            id: `msg_${Date.now()}_user`,\n            role: \"user\",\n            content: userMessage,\n            timestamp: new Date()\n        };\n        session.messages.push(userMsg);\n        // Prepare conversation history for AI\n        const conversationHistory = session.messages.slice(-10).map((msg)=>({\n                role: msg.role,\n                content: msg.content\n            }));\n        // Generate AI response with error handling\n        let aiResponse;\n        try {\n            aiResponse = await _lib_gemini__WEBPACK_IMPORTED_MODULE_0__.ChatbotService.generateChatResponse(userMessage, {\n                user: session.context.user,\n                conversationHistory,\n                systemState: session.context.systemState,\n                currentPage: session.context.currentPage\n            });\n        } catch (aiError) {\n            console.error(\"Error generating AI response:\", aiError);\n            // Fallback response\n            aiResponse = {\n                message: `I apologize, but I'm having trouble processing your message right now. However, I can still help you!\n\nHere are some things I can assist with:\n• **Document Management**: Creating, sending, and receiving documents\n• **System Navigation**: Finding features and understanding workflows\n• **Troubleshooting**: Solving common issues and problems\n• **General Help**: Answering questions about the system\n\nPlease try asking your question in a different way, or choose from the quick replies below.`,\n                actions: [\n                    {\n                        type: \"navigate\",\n                        label: \"\\uD83D\\uDCCA Dashboard\",\n                        data: {\n                            url: \"/dashboard\"\n                        }\n                    },\n                    {\n                        type: \"navigate\",\n                        label: \"\\uD83D\\uDCDD Create Document\",\n                        data: {\n                            url: \"/documents/add\"\n                        }\n                    },\n                    {\n                        type: \"help\",\n                        label: \"\\uD83D\\uDCDA Help Guide\",\n                        data: {\n                            topic: \"help\"\n                        }\n                    }\n                ],\n                quickReplies: [\n                    \"How to send documents?\",\n                    \"Check my inbox\",\n                    \"System help\",\n                    \"Contact support\"\n                ]\n            };\n        }\n        // Create assistant message\n        const assistantMsg = {\n            id: `msg_${Date.now()}_assistant`,\n            role: \"assistant\",\n            content: aiResponse.message,\n            timestamp: new Date(),\n            actions: aiResponse.actions,\n            quickReplies: aiResponse.quickReplies\n        };\n        session.messages.push(assistantMsg);\n        session.updatedAt = new Date();\n        return assistantMsg;\n    }\n    /**\n   * Get chat session\n   */ getSession(sessionId) {\n        return this.sessions.get(sessionId);\n    }\n    /**\n   * Update session context\n   */ updateSessionContext(sessionId, context) {\n        const session = this.sessions.get(sessionId);\n        if (session) {\n            session.context = {\n                ...session.context,\n                ...context\n            };\n            session.updatedAt = new Date();\n        }\n    }\n    /**\n   * Update session with document context for routing slip and journey data\n   */ updateDocumentContext(sessionId, documentData) {\n        const session = this.sessions.get(sessionId);\n        if (session) {\n            session.context.documentData = documentData;\n            session.updatedAt = new Date();\n        }\n    }\n    /**\n   * Get contextual help for current situation\n   */ async getContextualHelp(userId, context) {\n        return await _lib_gemini__WEBPACK_IMPORTED_MODULE_0__.ChatbotService.generateContextualHelp({\n            currentPage: context.currentPage,\n            userRole: context.user?.role,\n            recentActions: context.recentActions,\n            pendingDocuments: context.pendingDocuments,\n            commonIssues: context.commonIssues\n        });\n    }\n    /**\n   * Analyze user intent\n   */ async analyzeIntent(userMessage, context) {\n        return await _lib_gemini__WEBPACK_IMPORTED_MODULE_0__.ChatbotService.analyzeIntent(userMessage);\n    }\n    /**\n   * Generate intelligent welcome message based on user context\n   */ async generateWelcomeMessage(userContext) {\n        const user = userContext.user;\n        const currentPage = userContext.currentPage;\n        const userRole = user?.role || \"EMPLOYEE\";\n        const userDivision = user?.division || \"Unknown\";\n        let message = `👋 **Hello ${user?.name || \"there\"}!** I'm DocuBot, your intelligent AI assistant for the **Mines and Geosciences Bureau Regional Office No. II** Document Tracking System.`;\n        // Role-based welcome\n        if (userRole === \"ADMIN\" || userRole === \"REGIONAL_DIRECTOR\") {\n            message += `\\n\\n🔧 **Administrative Access Detected** - I can help you with user management, system reports, and administrative functions.`;\n        } else if (userRole === \"DIVISION_CHIEF\") {\n            message += `\\n\\n👥 **Division Chief Access** - I can assist with division management, workflow oversight, and team coordination.`;\n        } else {\n            message += `\\n\\n📄 **Employee Access** - I'm here to help with document management, workflow processes, and system navigation.`;\n        }\n        // Division-specific context\n        if (userDivision !== \"Unknown\") {\n            const divisionInfo = {\n                \"ORD\": \"Office of the Regional Director - Leadership and policy coordination\",\n                \"FAD\": \"Finance and Administrative Division - Financial and HR management\",\n                \"MMD\": \"Mines Management Division - Mining operations and permits\",\n                \"MSESDD\": \"Mines Safety, Environment and Social Development Division - Safety and environmental compliance\",\n                \"GSD\": \"Geological Survey Division - Geological research and surveys\"\n            };\n            if (divisionInfo[userDivision]) {\n                message += `\\n\\n🏢 **${userDivision} Division** - ${divisionInfo[userDivision]}`;\n            }\n        }\n        // Contextual welcome based on current page\n        if (currentPage?.includes(\"/documents/add\")) {\n            message += \"\\n\\n\\uD83D\\uDCDD **Creating a Document** - I can guide you through the document creation process, help you choose the right category and action type, and explain the workflow.\";\n        } else if (currentPage?.includes(\"/documents\") && currentPage?.includes(\"filter=inbox\")) {\n            message += \"\\n\\n\\uD83D\\uDCE5 **Inbox Management** - I can help you understand document statuses, process received documents, and manage your workflow efficiently.\";\n        } else if (currentPage?.includes(\"/dashboard\")) {\n            message += \"\\n\\n\\uD83D\\uDCCA **Dashboard Overview** - I can explain the statistics, help you navigate to different sections, and provide insights about your document activity.\";\n        } else if (currentPage?.includes(\"/admin\")) {\n            message += \"\\n\\n\\uD83D\\uDD27 **Administrative Panel** - I can assist with user management, system configuration, reports generation, and administrative tasks.\";\n        } else {\n            message += \"\\n\\n\\uD83C\\uDFAF **Ready to Help** - Ask me about any aspect of the document tracking system!\";\n        }\n        message += \"\\n\\n**\\uD83D\\uDCA1 What I can help you with:**\\n• Document creation and management\\n• Workflow processes and status tracking\\n• Advanced search and filtering\\n• System features and navigation\\n• Role-specific functions and permissions\";\n        const actions = [\n            {\n                type: \"help\",\n                label: \"\\uD83D\\uDE80 Quick Tour\",\n                data: {\n                    topic: \"getting_started\"\n                }\n            },\n            {\n                type: \"navigate\",\n                label: \"\\uD83D\\uDCCA Dashboard\",\n                data: {\n                    url: \"/dashboard\"\n                }\n            },\n            {\n                type: \"info\",\n                label: \"\\uD83C\\uDFAF System Features\",\n                data: {\n                    topic: \"features\"\n                }\n            }\n        ];\n        // Role-specific quick replies\n        let quickReplies = [];\n        if (userRole === \"ADMIN\" || userRole === \"REGIONAL_DIRECTOR\") {\n            quickReplies = [\n                \"User management help\",\n                \"System reports and analytics\",\n                \"Administrative functions\",\n                \"How to send documents?\"\n            ];\n        } else if (userRole === \"DIVISION_CHIEF\") {\n            quickReplies = [\n                \"Division workflow management\",\n                \"Team coordination\",\n                \"How to send documents?\",\n                \"Check my inbox\"\n            ];\n        } else {\n            quickReplies = [\n                \"How do I send a document?\",\n                \"Check my inbox\",\n                \"Document workflow help\",\n                \"Advanced search tips\"\n            ];\n        }\n        return {\n            message,\n            actions,\n            quickReplies\n        };\n    }\n    /**\n   * Clean up old sessions (call periodically)\n   */ cleanupOldSessions() {\n        // Increase session lifetime to 4 hours to reduce session loss\n        const fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);\n        let cleanedCount = 0;\n        for (const [sessionId, session] of this.sessions.entries()){\n            if (session.updatedAt < fourHoursAgo) {\n                this.sessions.delete(sessionId);\n                cleanedCount++;\n            }\n        }\n        if (cleanedCount > 0) {\n            console.log(`Cleaned up ${cleanedCount} old chat sessions`);\n        }\n    }\n    /**\n   * Check if session exists and is valid\n   */ hasValidSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        // Check if session is not too old (4 hours)\n        const fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);\n        return session.updatedAt > fourHoursAgo;\n    }\n    /**\n   * Get session statistics\n   */ getStats() {\n        const activeSessions = this.sessions.size;\n        let totalMessages = 0;\n        let totalSessionLength = 0;\n        for (const session of this.sessions.values()){\n            totalMessages += session.messages.length;\n            totalSessionLength += session.messages.length;\n        }\n        return {\n            activeSessions,\n            totalMessages,\n            averageSessionLength: activeSessions > 0 ? totalSessionLength / activeSessions : 0\n        };\n    }\n    constructor(){\n        this.sessions = new Map();\n    }\n}\n// Export singleton instance\nconst chatbotService = new ChatbotService();\n// Auto-cleanup old sessions every 30 minutes\nif (true) {\n    setInterval(()=>{\n        chatbotService.cleanupOldSessions();\n    }, 30 * 60 * 1000);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NoYXRib3QvY2hhdGJvdFNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFcUU7QUFrQ3RFLE1BQU1BO0lBR0o7O0dBRUMsR0FDRCxNQUFNRSxhQUFhQyxNQUFjLEVBQUVDLFdBQWdCLEVBQXdCO1FBQ3pFLE1BQU1DLFlBQVksQ0FBQyxLQUFLLEVBQUVGLE9BQU8sQ0FBQyxFQUFFRyxLQUFLQyxHQUFHLEdBQUcsQ0FBQztRQUVoRCxNQUFNQyxVQUF1QjtZQUMzQkMsSUFBSUo7WUFDSkY7WUFDQU8sVUFBVSxFQUFFO1lBQ1pDLFNBQVM7Z0JBQ1BDLE1BQU1SLFlBQVlRLElBQUk7Z0JBQ3RCQyxhQUFhVCxZQUFZUyxXQUFXO2dCQUNwQ0MsYUFBYVYsWUFBWVUsV0FBVztZQUN0QztZQUNBQyxXQUFXLElBQUlUO1lBQ2ZVLFdBQVcsSUFBSVY7UUFDakI7UUFFQSxzQkFBc0I7UUFDdEIsTUFBTVcsaUJBQWlCLE1BQU0sSUFBSSxDQUFDQyxzQkFBc0IsQ0FBQ2Q7UUFDekRJLFFBQVFFLFFBQVEsQ0FBQ1MsSUFBSSxDQUFDO1lBQ3BCVixJQUFJLENBQUMsSUFBSSxFQUFFSCxLQUFLQyxHQUFHLEdBQUcsQ0FBQztZQUN2QmEsTUFBTTtZQUNOQyxTQUFTSixlQUFlSyxPQUFPO1lBQy9CQyxXQUFXLElBQUlqQjtZQUNma0IsU0FBU1AsZUFBZU8sT0FBTztZQUMvQkMsY0FBY1IsZUFBZVEsWUFBWTtRQUMzQztRQUVBLElBQUksQ0FBQ0MsUUFBUSxDQUFDQyxHQUFHLENBQUN0QixXQUFXRztRQUM3QixPQUFPQTtJQUNUO0lBRUE7O0dBRUMsR0FDRCxNQUFNb0IsWUFBWXZCLFNBQWlCLEVBQUV3QixXQUFtQixFQUF3QjtRQUM5RSxJQUFJckIsVUFBVSxJQUFJLENBQUNrQixRQUFRLENBQUNJLEdBQUcsQ0FBQ3pCO1FBRWhDLDJEQUEyRDtRQUMzRCxJQUFJLENBQUNHLFNBQVM7WUFDWnVCLFFBQVFDLElBQUksQ0FBQyxDQUFDLGFBQWEsRUFBRTNCLFVBQVUsb0NBQW9DLENBQUM7WUFFNUUsNENBQTRDO1lBQzVDLE1BQU00QixjQUFjNUIsVUFBVTZCLEtBQUssQ0FBQztZQUNwQyxJQUFJRCxhQUFhO2dCQUNmLE1BQU05QixTQUFTOEIsV0FBVyxDQUFDLEVBQUU7Z0JBQzdCRixRQUFRSSxHQUFHLENBQUMsQ0FBQyw4QkFBOEIsRUFBRWhDLE9BQU8sQ0FBQztnQkFFckQsd0RBQXdEO2dCQUN4REssVUFBVTtvQkFDUkMsSUFBSUo7b0JBQ0pGO29CQUNBTyxVQUFVLEVBQUU7b0JBQ1pDLFNBQVM7d0JBQ1BDLE1BQU07NEJBQUVILElBQUlOO3dCQUFPO3dCQUNuQlUsYUFBYTt3QkFDYkMsYUFBYTs0QkFBRVMsV0FBVyxJQUFJakIsT0FBTzhCLFdBQVc7d0JBQUc7b0JBQ3JEO29CQUNBckIsV0FBVyxJQUFJVDtvQkFDZlUsV0FBVyxJQUFJVjtnQkFDakI7Z0JBRUEseUJBQXlCO2dCQUN6QixNQUFNK0Isa0JBQStCO29CQUNuQzVCLElBQUksQ0FBQyxJQUFJLEVBQUVILEtBQUtDLEdBQUcsR0FBRyxTQUFTLENBQUM7b0JBQ2hDYSxNQUFNO29CQUNOQyxTQUFTLENBQUM7Ozs7Ozs7OzJCQVFPLENBQUM7b0JBQ2xCRSxXQUFXLElBQUlqQjtvQkFDZmtCLFNBQVM7d0JBQ1A7NEJBQUVjLE1BQU07NEJBQVlDLE9BQU87NEJBQWdCQyxNQUFNO2dDQUFFQyxLQUFLOzRCQUFhO3dCQUFFO3dCQUN2RTs0QkFBRUgsTUFBTTs0QkFBWUMsT0FBTzs0QkFBc0JDLE1BQU07Z0NBQUVDLEtBQUs7NEJBQWlCO3dCQUFFO3dCQUNqRjs0QkFBRUgsTUFBTTs0QkFBUUMsT0FBTzs0QkFBaUJDLE1BQU07Z0NBQUVFLE9BQU87NEJBQU87d0JBQUU7cUJBQ2pFO29CQUNEakIsY0FBYzt3QkFBQzt3QkFBMEI7d0JBQWtCO3dCQUFxQjtxQkFBa0I7Z0JBQ3BHO2dCQUVBakIsUUFBUUUsUUFBUSxDQUFDUyxJQUFJLENBQUNrQjtnQkFDdEIsSUFBSSxDQUFDWCxRQUFRLENBQUNDLEdBQUcsQ0FBQ3RCLFdBQVdHO1lBQy9CLE9BQU87Z0JBQ0wsTUFBTSxJQUFJbUMsTUFBTTtZQUNsQjtRQUNGO1FBRUEsOEJBQThCO1FBQzlCLE1BQU1DLFVBQXVCO1lBQzNCbkMsSUFBSSxDQUFDLElBQUksRUFBRUgsS0FBS0MsR0FBRyxHQUFHLEtBQUssQ0FBQztZQUM1QmEsTUFBTTtZQUNOQyxTQUFTUTtZQUNUTixXQUFXLElBQUlqQjtRQUNqQjtRQUNBRSxRQUFRRSxRQUFRLENBQUNTLElBQUksQ0FBQ3lCO1FBRXRCLHNDQUFzQztRQUN0QyxNQUFNQyxzQkFBc0JyQyxRQUFRRSxRQUFRLENBQUNvQyxLQUFLLENBQUMsQ0FBQyxJQUFJQyxHQUFHLENBQUNDLENBQUFBLE1BQVE7Z0JBQ2xFNUIsTUFBTTRCLElBQUk1QixJQUFJO2dCQUNkQyxTQUFTMkIsSUFBSTNCLE9BQU87WUFDdEI7UUFFQSwyQ0FBMkM7UUFDM0MsSUFBSTRCO1FBQ0osSUFBSTtZQUNGQSxhQUFhLE1BQU1oRCx1REFBb0JBLENBQUNpRCxvQkFBb0IsQ0FBQ3JCLGFBQWE7Z0JBQ3hFakIsTUFBTUosUUFBUUcsT0FBTyxDQUFDQyxJQUFJO2dCQUMxQmlDO2dCQUNBL0IsYUFBYU4sUUFBUUcsT0FBTyxDQUFDRyxXQUFXO2dCQUN4Q0QsYUFBYUwsUUFBUUcsT0FBTyxDQUFDRSxXQUFXO1lBQzFDO1FBQ0YsRUFBRSxPQUFPc0MsU0FBYztZQUNyQnBCLFFBQVFxQixLQUFLLENBQUMsaUNBQWlDRDtZQUMvQyxvQkFBb0I7WUFDcEJGLGFBQWE7Z0JBQ1gzQixTQUFTLENBQUM7Ozs7Ozs7OzJGQVF5RSxDQUFDO2dCQUNwRkUsU0FBUztvQkFDUDt3QkFBRWMsTUFBTTt3QkFBWUMsT0FBTzt3QkFBZ0JDLE1BQU07NEJBQUVDLEtBQUs7d0JBQWE7b0JBQUU7b0JBQ3ZFO3dCQUFFSCxNQUFNO3dCQUFZQyxPQUFPO3dCQUFzQkMsTUFBTTs0QkFBRUMsS0FBSzt3QkFBaUI7b0JBQUU7b0JBQ2pGO3dCQUFFSCxNQUFNO3dCQUFRQyxPQUFPO3dCQUFpQkMsTUFBTTs0QkFBRUUsT0FBTzt3QkFBTztvQkFBRTtpQkFDakU7Z0JBQ0RqQixjQUFjO29CQUFDO29CQUEwQjtvQkFBa0I7b0JBQWU7aUJBQWtCO1lBQzlGO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTTRCLGVBQTRCO1lBQ2hDNUMsSUFBSSxDQUFDLElBQUksRUFBRUgsS0FBS0MsR0FBRyxHQUFHLFVBQVUsQ0FBQztZQUNqQ2EsTUFBTTtZQUNOQyxTQUFTNEIsV0FBVzNCLE9BQU87WUFDM0JDLFdBQVcsSUFBSWpCO1lBQ2ZrQixTQUFTeUIsV0FBV3pCLE9BQU87WUFDM0JDLGNBQWN3QixXQUFXeEIsWUFBWTtRQUN2QztRQUVBakIsUUFBUUUsUUFBUSxDQUFDUyxJQUFJLENBQUNrQztRQUN0QjdDLFFBQVFRLFNBQVMsR0FBRyxJQUFJVjtRQUV4QixPQUFPK0M7SUFDVDtJQUVBOztHQUVDLEdBQ0RDLFdBQVdqRCxTQUFpQixFQUEyQjtRQUNyRCxPQUFPLElBQUksQ0FBQ3FCLFFBQVEsQ0FBQ0ksR0FBRyxDQUFDekI7SUFDM0I7SUFFQTs7R0FFQyxHQUNEa0QscUJBQXFCbEQsU0FBaUIsRUFBRU0sT0FBd0MsRUFBUTtRQUN0RixNQUFNSCxVQUFVLElBQUksQ0FBQ2tCLFFBQVEsQ0FBQ0ksR0FBRyxDQUFDekI7UUFDbEMsSUFBSUcsU0FBUztZQUNYQSxRQUFRRyxPQUFPLEdBQUc7Z0JBQUUsR0FBR0gsUUFBUUcsT0FBTztnQkFBRSxHQUFHQSxPQUFPO1lBQUM7WUFDbkRILFFBQVFRLFNBQVMsR0FBRyxJQUFJVjtRQUMxQjtJQUNGO0lBRUE7O0dBRUMsR0FDRGtELHNCQUFzQm5ELFNBQWlCLEVBQUVvRCxZQUt4QyxFQUFRO1FBQ1AsTUFBTWpELFVBQVUsSUFBSSxDQUFDa0IsUUFBUSxDQUFDSSxHQUFHLENBQUN6QjtRQUNsQyxJQUFJRyxTQUFTO1lBQ1hBLFFBQVFHLE9BQU8sQ0FBQzhDLFlBQVksR0FBR0E7WUFDL0JqRCxRQUFRUSxTQUFTLEdBQUcsSUFBSVY7UUFDMUI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTW9ELGtCQUFrQnZELE1BQWMsRUFBRVEsT0FBWSxFQUFnQjtRQUNsRSxPQUFPLE1BQU1WLHVEQUFvQkEsQ0FBQzBELHNCQUFzQixDQUFDO1lBQ3ZEOUMsYUFBYUYsUUFBUUUsV0FBVztZQUNoQytDLFVBQVVqRCxRQUFRQyxJQUFJLEVBQUVRO1lBQ3hCeUMsZUFBZWxELFFBQVFrRCxhQUFhO1lBQ3BDQyxrQkFBa0JuRCxRQUFRbUQsZ0JBQWdCO1lBQzFDQyxjQUFjcEQsUUFBUW9ELFlBQVk7UUFDcEM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsY0FBY25DLFdBQW1CLEVBQUVsQixPQUFZLEVBQWdCO1FBQ25FLE9BQU8sTUFBTVYsdURBQW9CQSxDQUFDK0QsYUFBYSxDQUFDbkM7SUFDbEQ7SUFFQTs7R0FFQyxHQUNELE1BQWNYLHVCQUF1QmQsV0FBZ0IsRUFJbEQ7UUFDRCxNQUFNUSxPQUFPUixZQUFZUSxJQUFJO1FBQzdCLE1BQU1DLGNBQWNULFlBQVlTLFdBQVc7UUFDM0MsTUFBTStDLFdBQVdoRCxNQUFNUSxRQUFRO1FBQy9CLE1BQU02QyxlQUFlckQsTUFBTXNELFlBQVk7UUFFdkMsSUFBSTVDLFVBQVUsQ0FBQyxXQUFXLEVBQUVWLE1BQU11RCxRQUFRLFFBQVEsd0lBQXdJLENBQUM7UUFFM0wscUJBQXFCO1FBQ3JCLElBQUlQLGFBQWEsV0FBV0EsYUFBYSxxQkFBcUI7WUFDNUR0QyxXQUFXLENBQUMsOEhBQThILENBQUM7UUFDN0ksT0FBTyxJQUFJc0MsYUFBYSxrQkFBa0I7WUFDeEN0QyxXQUFXLENBQUMsb0hBQW9ILENBQUM7UUFDbkksT0FBTztZQUNMQSxXQUFXLENBQUMsa0hBQWtILENBQUM7UUFDakk7UUFFQSw0QkFBNEI7UUFDNUIsSUFBSTJDLGlCQUFpQixXQUFXO1lBQzlCLE1BQU1HLGVBQWU7Z0JBQ25CLE9BQU87Z0JBQ1AsT0FBTztnQkFDUCxPQUFPO2dCQUNQLFVBQVU7Z0JBQ1YsT0FBTztZQUNUO1lBRUEsSUFBSUEsWUFBWSxDQUFDSCxhQUFhLEVBQUU7Z0JBQzlCM0MsV0FBVyxDQUFDLFNBQVMsRUFBRTJDLGFBQWEsY0FBYyxFQUFFRyxZQUFZLENBQUNILGFBQWEsQ0FBQyxDQUFDO1lBQ2xGO1FBQ0Y7UUFFQSwyQ0FBMkM7UUFDM0MsSUFBSXBELGFBQWF3RCxTQUFTLG1CQUFtQjtZQUMzQy9DLFdBQVc7UUFDYixPQUFPLElBQUlULGFBQWF3RCxTQUFTLGlCQUFpQnhELGFBQWF3RCxTQUFTLGlCQUFpQjtZQUN2Ri9DLFdBQVc7UUFDYixPQUFPLElBQUlULGFBQWF3RCxTQUFTLGVBQWU7WUFDOUMvQyxXQUFXO1FBQ2IsT0FBTyxJQUFJVCxhQUFhd0QsU0FBUyxXQUFXO1lBQzFDL0MsV0FBVztRQUNiLE9BQU87WUFDTEEsV0FBVztRQUNiO1FBRUFBLFdBQVc7UUFFWCxNQUFNRSxVQUFVO1lBQ2Q7Z0JBQUVjLE1BQU07Z0JBQVFDLE9BQU87Z0JBQWlCQyxNQUFNO29CQUFFRSxPQUFPO2dCQUFrQjtZQUFFO1lBQzNFO2dCQUFFSixNQUFNO2dCQUFZQyxPQUFPO2dCQUFnQkMsTUFBTTtvQkFBRUMsS0FBSztnQkFBYTtZQUFFO1lBQ3ZFO2dCQUFFSCxNQUFNO2dCQUFRQyxPQUFPO2dCQUFzQkMsTUFBTTtvQkFBRUUsT0FBTztnQkFBVztZQUFFO1NBQzFFO1FBRUQsOEJBQThCO1FBQzlCLElBQUlqQixlQUFlLEVBQUU7UUFDckIsSUFBSW1DLGFBQWEsV0FBV0EsYUFBYSxxQkFBcUI7WUFDNURuQyxlQUFlO2dCQUNiO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSCxPQUFPLElBQUltQyxhQUFhLGtCQUFrQjtZQUN4Q25DLGVBQWU7Z0JBQ2I7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNILE9BQU87WUFDTEEsZUFBZTtnQkFDYjtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1FBQ0g7UUFFQSxPQUFPO1lBQUVIO1lBQVNFO1lBQVNDO1FBQWE7SUFDMUM7SUFFQTs7R0FFQyxHQUNENkMscUJBQTJCO1FBQ3pCLDhEQUE4RDtRQUM5RCxNQUFNQyxlQUFlLElBQUlqRSxLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUs7UUFFekQsSUFBSWlFLGVBQWU7UUFDbkIsS0FBSyxNQUFNLENBQUNuRSxXQUFXRyxRQUFRLElBQUksSUFBSSxDQUFDa0IsUUFBUSxDQUFDK0MsT0FBTyxHQUFJO1lBQzFELElBQUlqRSxRQUFRUSxTQUFTLEdBQUd1RCxjQUFjO2dCQUNwQyxJQUFJLENBQUM3QyxRQUFRLENBQUNnRCxNQUFNLENBQUNyRTtnQkFDckJtRTtZQUNGO1FBQ0Y7UUFFQSxJQUFJQSxlQUFlLEdBQUc7WUFDcEJ6QyxRQUFRSSxHQUFHLENBQUMsQ0FBQyxXQUFXLEVBQUVxQyxhQUFhLGtCQUFrQixDQUFDO1FBQzVEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNERyxnQkFBZ0J0RSxTQUFpQixFQUFXO1FBQzFDLE1BQU1HLFVBQVUsSUFBSSxDQUFDa0IsUUFBUSxDQUFDSSxHQUFHLENBQUN6QjtRQUNsQyxJQUFJLENBQUNHLFNBQVMsT0FBTztRQUVyQiw0Q0FBNEM7UUFDNUMsTUFBTStELGVBQWUsSUFBSWpFLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSztRQUN6RCxPQUFPQyxRQUFRUSxTQUFTLEdBQUd1RDtJQUM3QjtJQUVBOztHQUVDLEdBQ0RLLFdBSUU7UUFDQSxNQUFNQyxpQkFBaUIsSUFBSSxDQUFDbkQsUUFBUSxDQUFDb0QsSUFBSTtRQUN6QyxJQUFJQyxnQkFBZ0I7UUFDcEIsSUFBSUMscUJBQXFCO1FBRXpCLEtBQUssTUFBTXhFLFdBQVcsSUFBSSxDQUFDa0IsUUFBUSxDQUFDdUQsTUFBTSxHQUFJO1lBQzVDRixpQkFBaUJ2RSxRQUFRRSxRQUFRLENBQUN3RSxNQUFNO1lBQ3hDRixzQkFBc0J4RSxRQUFRRSxRQUFRLENBQUN3RSxNQUFNO1FBQy9DO1FBRUEsT0FBTztZQUNMTDtZQUNBRTtZQUNBSSxzQkFBc0JOLGlCQUFpQixJQUFJRyxxQkFBcUJILGlCQUFpQjtRQUNuRjtJQUNGOzthQWpXUW5ELFdBQXFDLElBQUkwRDs7QUFrV25EO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU1DLGlCQUFpQixJQUFJckYsaUJBQWlCO0FBRW5ELDZDQUE2QztBQUM3QyxJQUFJLElBQWtCLEVBQWE7SUFDakNzRixZQUFZO1FBQ1ZELGVBQWVmLGtCQUFrQjtJQUNuQyxHQUFHLEtBQUssS0FBSztBQUNmIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZG9jdW1lbnQtdHJhY2tlci8uL3NyYy9saWIvY2hhdGJvdC9jaGF0Ym90U2VydmljZS50cz9mYWRiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ2hhdGJvdCBTZXJ2aWNlXG4gKiBNYW5hZ2VzIGNvbnZlcnNhdGlvbiBzdGF0ZSBhbmQgaW50ZWdyYXRlcyB3aXRoIEdlbWluaSBBSVxuICovXG5cbmltcG9ydCB7IENoYXRib3RTZXJ2aWNlIGFzIEdlbWluaUNoYXRib3RTZXJ2aWNlIH0gZnJvbSAnQC9saWIvZ2VtaW5pJztcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHJvbGU6ICd1c2VyJyB8ICdhc3Npc3RhbnQnO1xuICBjb250ZW50OiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogRGF0ZTtcbiAgYWN0aW9ucz86IEFycmF5PHtcbiAgICB0eXBlOiAnbmF2aWdhdGUnIHwgJ3NlYXJjaCcgfCAnY3JlYXRlJyB8ICdoZWxwJyB8ICdpbmZvJztcbiAgICBsYWJlbDogc3RyaW5nO1xuICAgIGRhdGE/OiBhbnk7XG4gIH0+O1xuICBxdWlja1JlcGxpZXM/OiBzdHJpbmdbXTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGF0U2Vzc2lvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJJZDogc3RyaW5nO1xuICBtZXNzYWdlczogQ2hhdE1lc3NhZ2VbXTtcbiAgY29udGV4dDoge1xuICAgIHVzZXI/OiBhbnk7XG4gICAgY3VycmVudFBhZ2U/OiBzdHJpbmc7XG4gICAgc3lzdGVtU3RhdGU/OiBhbnk7XG4gICAgZG9jdW1lbnREYXRhPzoge1xuICAgICAgY3VycmVudERvY3VtZW50PzogYW55O1xuICAgICAgZG9jdW1lbnRKb3VybmV5PzogYW55W107XG4gICAgICByb3V0aW5nU2xpcD86IGFueVtdO1xuICAgICAgcmVsYXRlZERvY3VtZW50cz86IGFueVtdO1xuICAgIH07XG4gIH07XG4gIGNyZWF0ZWRBdDogRGF0ZTtcbiAgdXBkYXRlZEF0OiBEYXRlO1xufVxuXG5jbGFzcyBDaGF0Ym90U2VydmljZSB7XG4gIHByaXZhdGUgc2Vzc2lvbnM6IE1hcDxzdHJpbmcsIENoYXRTZXNzaW9uPiA9IG5ldyBNYXAoKTtcblxuICAvKipcbiAgICogU3RhcnQgYSBuZXcgY2hhdCBzZXNzaW9uXG4gICAqL1xuICBhc3luYyBzdGFydFNlc3Npb24odXNlcklkOiBzdHJpbmcsIHVzZXJDb250ZXh0OiBhbnkpOiBQcm9taXNlPENoYXRTZXNzaW9uPiB7XG4gICAgY29uc3Qgc2Vzc2lvbklkID0gYGNoYXRfJHt1c2VySWR9XyR7RGF0ZS5ub3coKX1gO1xuXG4gICAgY29uc3Qgc2Vzc2lvbjogQ2hhdFNlc3Npb24gPSB7XG4gICAgICBpZDogc2Vzc2lvbklkLFxuICAgICAgdXNlcklkLFxuICAgICAgbWVzc2FnZXM6IFtdLFxuICAgICAgY29udGV4dDoge1xuICAgICAgICB1c2VyOiB1c2VyQ29udGV4dC51c2VyLFxuICAgICAgICBjdXJyZW50UGFnZTogdXNlckNvbnRleHQuY3VycmVudFBhZ2UsXG4gICAgICAgIHN5c3RlbVN0YXRlOiB1c2VyQ29udGV4dC5zeXN0ZW1TdGF0ZVxuICAgICAgfSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKVxuICAgIH07XG5cbiAgICAvLyBBZGQgd2VsY29tZSBtZXNzYWdlXG4gICAgY29uc3Qgd2VsY29tZU1lc3NhZ2UgPSBhd2FpdCB0aGlzLmdlbmVyYXRlV2VsY29tZU1lc3NhZ2UodXNlckNvbnRleHQpO1xuICAgIHNlc3Npb24ubWVzc2FnZXMucHVzaCh7XG4gICAgICBpZDogYG1zZ18ke0RhdGUubm93KCl9YCxcbiAgICAgIHJvbGU6ICdhc3Npc3RhbnQnLFxuICAgICAgY29udGVudDogd2VsY29tZU1lc3NhZ2UubWVzc2FnZSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIGFjdGlvbnM6IHdlbGNvbWVNZXNzYWdlLmFjdGlvbnMsXG4gICAgICBxdWlja1JlcGxpZXM6IHdlbGNvbWVNZXNzYWdlLnF1aWNrUmVwbGllc1xuICAgIH0pO1xuXG4gICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBzZXNzaW9uKTtcbiAgICByZXR1cm4gc2Vzc2lvbjtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZW5kIGEgbWVzc2FnZSBhbmQgZ2V0IHJlc3BvbnNlXG4gICAqL1xuICBhc3luYyBzZW5kTWVzc2FnZShzZXNzaW9uSWQ6IHN0cmluZywgdXNlck1lc3NhZ2U6IHN0cmluZyk6IFByb21pc2U8Q2hhdE1lc3NhZ2U+IHtcbiAgICBsZXQgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG5cbiAgICAvLyBJZiBzZXNzaW9uIG5vdCBmb3VuZCwgdHJ5IHRvIHJlY292ZXIgb3IgY3JlYXRlIGEgbmV3IG9uZVxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgY29uc29sZS53YXJuKGBDaGF0IHNlc3Npb24gJHtzZXNzaW9uSWR9IG5vdCBmb3VuZCwgYXR0ZW1wdGluZyB0byByZWNvdmVyLi4uYCk7XG5cbiAgICAgIC8vIEV4dHJhY3QgdXNlcklkIGZyb20gc2Vzc2lvbklkIGlmIHBvc3NpYmxlXG4gICAgICBjb25zdCB1c2VySWRNYXRjaCA9IHNlc3Npb25JZC5tYXRjaCgvXmNoYXRfKC4rKV9cXGQrJC8pO1xuICAgICAgaWYgKHVzZXJJZE1hdGNoKSB7XG4gICAgICAgIGNvbnN0IHVzZXJJZCA9IHVzZXJJZE1hdGNoWzFdO1xuICAgICAgICBjb25zb2xlLmxvZyhgQ3JlYXRpbmcgbmV3IHNlc3Npb24gZm9yIHVzZXIgJHt1c2VySWR9YCk7XG5cbiAgICAgICAgLy8gQ3JlYXRlIGEgbWluaW1hbCBzZXNzaW9uIHRvIGNvbnRpbnVlIHRoZSBjb252ZXJzYXRpb25cbiAgICAgICAgc2Vzc2lvbiA9IHtcbiAgICAgICAgICBpZDogc2Vzc2lvbklkLFxuICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICBtZXNzYWdlczogW10sXG4gICAgICAgICAgY29udGV4dDoge1xuICAgICAgICAgICAgdXNlcjogeyBpZDogdXNlcklkIH0sXG4gICAgICAgICAgICBjdXJyZW50UGFnZTogJy9kb2N1bWVudHMnLFxuICAgICAgICAgICAgc3lzdGVtU3RhdGU6IHsgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKVxuICAgICAgICB9O1xuXG4gICAgICAgIC8vIEFkZCBhIHJlY292ZXJ5IG1lc3NhZ2VcbiAgICAgICAgY29uc3QgcmVjb3ZlcnlNZXNzYWdlOiBDaGF0TWVzc2FnZSA9IHtcbiAgICAgICAgICBpZDogYG1zZ18ke0RhdGUubm93KCl9X3JlY292ZXJ5YCxcbiAgICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgICAgICBjb250ZW50OiBg8J+RiyAqKldlbGNvbWUgYmFjayEqKiBJIG5vdGljZWQgb3VyIGNvbnZlcnNhdGlvbiB3YXMgaW50ZXJydXB0ZWQsIGJ1dCBJJ20gaGVyZSB0byBoZWxwIHlvdSB3aXRoIHRoZSBEb2N1bWVudCBUcmFja2luZyBTeXN0ZW0uXG5cbioq8J+UhCBTZXNzaW9uIFJlY292ZXJlZCoqIC0gWW91IGNhbiBjb250aW51ZSBhc2tpbmcgcXVlc3Rpb25zIGFib3V0Olxu4oCiIERvY3VtZW50IG1hbmFnZW1lbnQgYW5kIHdvcmtmbG93c1xu4oCiIFN5c3RlbSBuYXZpZ2F0aW9uIGFuZCBmZWF0dXJlc1xu4oCiIFJvdXRpbmcgc2xpcHMgYW5kIGRvY3VtZW50IGpvdXJuZXlzXG7igKIgVHJvdWJsZXNob290aW5nIGFuZCBzdXBwb3J0XG5cbkhvdyBjYW4gSSBhc3Npc3QgeW91IHRvZGF5P2AsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgIGFjdGlvbnM6IFtcbiAgICAgICAgICAgIHsgdHlwZTogJ25hdmlnYXRlJywgbGFiZWw6ICfwn5OKIERhc2hib2FyZCcsIGRhdGE6IHsgdXJsOiAnL2Rhc2hib2FyZCcgfSB9LFxuICAgICAgICAgICAgeyB0eXBlOiAnbmF2aWdhdGUnLCBsYWJlbDogJ/Cfk50gQ3JlYXRlIERvY3VtZW50JywgZGF0YTogeyB1cmw6ICcvZG9jdW1lbnRzL2FkZCcgfSB9LFxuICAgICAgICAgICAgeyB0eXBlOiAnaGVscCcsIGxhYmVsOiAn8J+TmiBIZWxwIEd1aWRlJywgZGF0YTogeyB0b3BpYzogJ2hlbHAnIH0gfVxuICAgICAgICAgIF0sXG4gICAgICAgICAgcXVpY2tSZXBsaWVzOiBbJ0hvdyB0byBzZW5kIGRvY3VtZW50cz8nLCAnQ2hlY2sgbXkgaW5ib3gnLCAnRG9jdW1lbnQgd29ya2Zsb3cnLCAnU3lzdGVtIGZlYXR1cmVzJ11cbiAgICAgICAgfTtcblxuICAgICAgICBzZXNzaW9uLm1lc3NhZ2VzLnB1c2gocmVjb3ZlcnlNZXNzYWdlKTtcbiAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBzZXNzaW9uKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ2hhdCBzZXNzaW9uIG5vdCBmb3VuZCBhbmQgY2Fubm90IGJlIHJlY292ZXJlZC4gUGxlYXNlIHJlZnJlc2ggdGhlIHBhZ2UgdG8gc3RhcnQgYSBuZXcgc2Vzc2lvbi4nKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBZGQgdXNlciBtZXNzYWdlIHRvIHNlc3Npb25cbiAgICBjb25zdCB1c2VyTXNnOiBDaGF0TWVzc2FnZSA9IHtcbiAgICAgIGlkOiBgbXNnXyR7RGF0ZS5ub3coKX1fdXNlcmAsXG4gICAgICByb2xlOiAndXNlcicsXG4gICAgICBjb250ZW50OiB1c2VyTWVzc2FnZSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgIH07XG4gICAgc2Vzc2lvbi5tZXNzYWdlcy5wdXNoKHVzZXJNc2cpO1xuXG4gICAgLy8gUHJlcGFyZSBjb252ZXJzYXRpb24gaGlzdG9yeSBmb3IgQUlcbiAgICBjb25zdCBjb252ZXJzYXRpb25IaXN0b3J5ID0gc2Vzc2lvbi5tZXNzYWdlcy5zbGljZSgtMTApLm1hcChtc2cgPT4gKHtcbiAgICAgIHJvbGU6IG1zZy5yb2xlLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnRcbiAgICB9KSk7XG5cbiAgICAvLyBHZW5lcmF0ZSBBSSByZXNwb25zZSB3aXRoIGVycm9yIGhhbmRsaW5nXG4gICAgbGV0IGFpUmVzcG9uc2U7XG4gICAgdHJ5IHtcbiAgICAgIGFpUmVzcG9uc2UgPSBhd2FpdCBHZW1pbmlDaGF0Ym90U2VydmljZS5nZW5lcmF0ZUNoYXRSZXNwb25zZSh1c2VyTWVzc2FnZSwge1xuICAgICAgICB1c2VyOiBzZXNzaW9uLmNvbnRleHQudXNlcixcbiAgICAgICAgY29udmVyc2F0aW9uSGlzdG9yeSxcbiAgICAgICAgc3lzdGVtU3RhdGU6IHNlc3Npb24uY29udGV4dC5zeXN0ZW1TdGF0ZSxcbiAgICAgICAgY3VycmVudFBhZ2U6IHNlc3Npb24uY29udGV4dC5jdXJyZW50UGFnZVxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoYWlFcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIEFJIHJlc3BvbnNlOicsIGFpRXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgcmVzcG9uc2VcbiAgICAgIGFpUmVzcG9uc2UgPSB7XG4gICAgICAgIG1lc3NhZ2U6IGBJIGFwb2xvZ2l6ZSwgYnV0IEknbSBoYXZpbmcgdHJvdWJsZSBwcm9jZXNzaW5nIHlvdXIgbWVzc2FnZSByaWdodCBub3cuIEhvd2V2ZXIsIEkgY2FuIHN0aWxsIGhlbHAgeW91IVxuXG5IZXJlIGFyZSBzb21lIHRoaW5ncyBJIGNhbiBhc3Npc3Qgd2l0aDpcbuKAoiAqKkRvY3VtZW50IE1hbmFnZW1lbnQqKjogQ3JlYXRpbmcsIHNlbmRpbmcsIGFuZCByZWNlaXZpbmcgZG9jdW1lbnRzXG7igKIgKipTeXN0ZW0gTmF2aWdhdGlvbioqOiBGaW5kaW5nIGZlYXR1cmVzIGFuZCB1bmRlcnN0YW5kaW5nIHdvcmtmbG93c1xu4oCiICoqVHJvdWJsZXNob290aW5nKio6IFNvbHZpbmcgY29tbW9uIGlzc3VlcyBhbmQgcHJvYmxlbXNcbuKAoiAqKkdlbmVyYWwgSGVscCoqOiBBbnN3ZXJpbmcgcXVlc3Rpb25zIGFib3V0IHRoZSBzeXN0ZW1cblxuUGxlYXNlIHRyeSBhc2tpbmcgeW91ciBxdWVzdGlvbiBpbiBhIGRpZmZlcmVudCB3YXksIG9yIGNob29zZSBmcm9tIHRoZSBxdWljayByZXBsaWVzIGJlbG93LmAsXG4gICAgICAgIGFjdGlvbnM6IFtcbiAgICAgICAgICB7IHR5cGU6ICduYXZpZ2F0ZScsIGxhYmVsOiAn8J+TiiBEYXNoYm9hcmQnLCBkYXRhOiB7IHVybDogJy9kYXNoYm9hcmQnIH0gfSxcbiAgICAgICAgICB7IHR5cGU6ICduYXZpZ2F0ZScsIGxhYmVsOiAn8J+TnSBDcmVhdGUgRG9jdW1lbnQnLCBkYXRhOiB7IHVybDogJy9kb2N1bWVudHMvYWRkJyB9IH0sXG4gICAgICAgICAgeyB0eXBlOiAnaGVscCcsIGxhYmVsOiAn8J+TmiBIZWxwIEd1aWRlJywgZGF0YTogeyB0b3BpYzogJ2hlbHAnIH0gfVxuICAgICAgICBdLFxuICAgICAgICBxdWlja1JlcGxpZXM6IFsnSG93IHRvIHNlbmQgZG9jdW1lbnRzPycsICdDaGVjayBteSBpbmJveCcsICdTeXN0ZW0gaGVscCcsICdDb250YWN0IHN1cHBvcnQnXVxuICAgICAgfTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgYXNzaXN0YW50IG1lc3NhZ2VcbiAgICBjb25zdCBhc3Npc3RhbnRNc2c6IENoYXRNZXNzYWdlID0ge1xuICAgICAgaWQ6IGBtc2dfJHtEYXRlLm5vdygpfV9hc3Npc3RhbnRgLFxuICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXG4gICAgICBjb250ZW50OiBhaVJlc3BvbnNlLm1lc3NhZ2UsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICBhY3Rpb25zOiBhaVJlc3BvbnNlLmFjdGlvbnMsXG4gICAgICBxdWlja1JlcGxpZXM6IGFpUmVzcG9uc2UucXVpY2tSZXBsaWVzXG4gICAgfTtcblxuICAgIHNlc3Npb24ubWVzc2FnZXMucHVzaChhc3Npc3RhbnRNc2cpO1xuICAgIHNlc3Npb24udXBkYXRlZEF0ID0gbmV3IERhdGUoKTtcblxuICAgIHJldHVybiBhc3Npc3RhbnRNc2c7XG4gIH1cblxuICAvKipcbiAgICogR2V0IGNoYXQgc2Vzc2lvblxuICAgKi9cbiAgZ2V0U2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZyk6IENoYXRTZXNzaW9uIHwgdW5kZWZpbmVkIHtcbiAgICByZXR1cm4gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgc2Vzc2lvbiBjb250ZXh0XG4gICAqL1xuICB1cGRhdGVTZXNzaW9uQ29udGV4dChzZXNzaW9uSWQ6IHN0cmluZywgY29udGV4dDogUGFydGlhbDxDaGF0U2Vzc2lvblsnY29udGV4dCddPik6IHZvaWQge1xuICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgIGlmIChzZXNzaW9uKSB7XG4gICAgICBzZXNzaW9uLmNvbnRleHQgPSB7IC4uLnNlc3Npb24uY29udGV4dCwgLi4uY29udGV4dCB9O1xuICAgICAgc2Vzc2lvbi51cGRhdGVkQXQgPSBuZXcgRGF0ZSgpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgc2Vzc2lvbiB3aXRoIGRvY3VtZW50IGNvbnRleHQgZm9yIHJvdXRpbmcgc2xpcCBhbmQgam91cm5leSBkYXRhXG4gICAqL1xuICB1cGRhdGVEb2N1bWVudENvbnRleHQoc2Vzc2lvbklkOiBzdHJpbmcsIGRvY3VtZW50RGF0YToge1xuICAgIGN1cnJlbnREb2N1bWVudD86IGFueTtcbiAgICBkb2N1bWVudEpvdXJuZXk/OiBhbnlbXTtcbiAgICByb3V0aW5nU2xpcD86IGFueVtdO1xuICAgIHJlbGF0ZWREb2N1bWVudHM/OiBhbnlbXTtcbiAgfSk6IHZvaWQge1xuICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgIGlmIChzZXNzaW9uKSB7XG4gICAgICBzZXNzaW9uLmNvbnRleHQuZG9jdW1lbnREYXRhID0gZG9jdW1lbnREYXRhO1xuICAgICAgc2Vzc2lvbi51cGRhdGVkQXQgPSBuZXcgRGF0ZSgpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY29udGV4dHVhbCBoZWxwIGZvciBjdXJyZW50IHNpdHVhdGlvblxuICAgKi9cbiAgYXN5bmMgZ2V0Q29udGV4dHVhbEhlbHAodXNlcklkOiBzdHJpbmcsIGNvbnRleHQ6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgcmV0dXJuIGF3YWl0IEdlbWluaUNoYXRib3RTZXJ2aWNlLmdlbmVyYXRlQ29udGV4dHVhbEhlbHAoe1xuICAgICAgY3VycmVudFBhZ2U6IGNvbnRleHQuY3VycmVudFBhZ2UsXG4gICAgICB1c2VyUm9sZTogY29udGV4dC51c2VyPy5yb2xlLFxuICAgICAgcmVjZW50QWN0aW9uczogY29udGV4dC5yZWNlbnRBY3Rpb25zLFxuICAgICAgcGVuZGluZ0RvY3VtZW50czogY29udGV4dC5wZW5kaW5nRG9jdW1lbnRzLFxuICAgICAgY29tbW9uSXNzdWVzOiBjb250ZXh0LmNvbW1vbklzc3Vlc1xuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIEFuYWx5emUgdXNlciBpbnRlbnRcbiAgICovXG4gIGFzeW5jIGFuYWx5emVJbnRlbnQodXNlck1lc3NhZ2U6IHN0cmluZywgY29udGV4dDogYW55KTogUHJvbWlzZTxhbnk+IHtcbiAgICByZXR1cm4gYXdhaXQgR2VtaW5pQ2hhdGJvdFNlcnZpY2UuYW5hbHl6ZUludGVudCh1c2VyTWVzc2FnZSk7XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgaW50ZWxsaWdlbnQgd2VsY29tZSBtZXNzYWdlIGJhc2VkIG9uIHVzZXIgY29udGV4dFxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBnZW5lcmF0ZVdlbGNvbWVNZXNzYWdlKHVzZXJDb250ZXh0OiBhbnkpOiBQcm9taXNlPHtcbiAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgYWN0aW9ucz86IGFueVtdO1xuICAgIHF1aWNrUmVwbGllcz86IHN0cmluZ1tdO1xuICB9PiB7XG4gICAgY29uc3QgdXNlciA9IHVzZXJDb250ZXh0LnVzZXI7XG4gICAgY29uc3QgY3VycmVudFBhZ2UgPSB1c2VyQ29udGV4dC5jdXJyZW50UGFnZTtcbiAgICBjb25zdCB1c2VyUm9sZSA9IHVzZXI/LnJvbGUgfHwgJ0VNUExPWUVFJztcbiAgICBjb25zdCB1c2VyRGl2aXNpb24gPSB1c2VyPy5kaXZpc2lvbiB8fCAnVW5rbm93bic7XG5cbiAgICBsZXQgbWVzc2FnZSA9IGDwn5GLICoqSGVsbG8gJHt1c2VyPy5uYW1lIHx8ICd0aGVyZSd9ISoqIEknbSBEb2N1Qm90LCB5b3VyIGludGVsbGlnZW50IEFJIGFzc2lzdGFudCBmb3IgdGhlICoqTWluZXMgYW5kIEdlb3NjaWVuY2VzIEJ1cmVhdSBSZWdpb25hbCBPZmZpY2UgTm8uIElJKiogRG9jdW1lbnQgVHJhY2tpbmcgU3lzdGVtLmA7XG5cbiAgICAvLyBSb2xlLWJhc2VkIHdlbGNvbWVcbiAgICBpZiAodXNlclJvbGUgPT09ICdBRE1JTicgfHwgdXNlclJvbGUgPT09ICdSRUdJT05BTF9ESVJFQ1RPUicpIHtcbiAgICAgIG1lc3NhZ2UgKz0gYFxcblxcbvCflKcgKipBZG1pbmlzdHJhdGl2ZSBBY2Nlc3MgRGV0ZWN0ZWQqKiAtIEkgY2FuIGhlbHAgeW91IHdpdGggdXNlciBtYW5hZ2VtZW50LCBzeXN0ZW0gcmVwb3J0cywgYW5kIGFkbWluaXN0cmF0aXZlIGZ1bmN0aW9ucy5gO1xuICAgIH0gZWxzZSBpZiAodXNlclJvbGUgPT09ICdESVZJU0lPTl9DSElFRicpIHtcbiAgICAgIG1lc3NhZ2UgKz0gYFxcblxcbvCfkaUgKipEaXZpc2lvbiBDaGllZiBBY2Nlc3MqKiAtIEkgY2FuIGFzc2lzdCB3aXRoIGRpdmlzaW9uIG1hbmFnZW1lbnQsIHdvcmtmbG93IG92ZXJzaWdodCwgYW5kIHRlYW0gY29vcmRpbmF0aW9uLmA7XG4gICAgfSBlbHNlIHtcbiAgICAgIG1lc3NhZ2UgKz0gYFxcblxcbvCfk4QgKipFbXBsb3llZSBBY2Nlc3MqKiAtIEknbSBoZXJlIHRvIGhlbHAgd2l0aCBkb2N1bWVudCBtYW5hZ2VtZW50LCB3b3JrZmxvdyBwcm9jZXNzZXMsIGFuZCBzeXN0ZW0gbmF2aWdhdGlvbi5gO1xuICAgIH1cblxuICAgIC8vIERpdmlzaW9uLXNwZWNpZmljIGNvbnRleHRcbiAgICBpZiAodXNlckRpdmlzaW9uICE9PSAnVW5rbm93bicpIHtcbiAgICAgIGNvbnN0IGRpdmlzaW9uSW5mbyA9IHtcbiAgICAgICAgJ09SRCc6ICdPZmZpY2Ugb2YgdGhlIFJlZ2lvbmFsIERpcmVjdG9yIC0gTGVhZGVyc2hpcCBhbmQgcG9saWN5IGNvb3JkaW5hdGlvbicsXG4gICAgICAgICdGQUQnOiAnRmluYW5jZSBhbmQgQWRtaW5pc3RyYXRpdmUgRGl2aXNpb24gLSBGaW5hbmNpYWwgYW5kIEhSIG1hbmFnZW1lbnQnLFxuICAgICAgICAnTU1EJzogJ01pbmVzIE1hbmFnZW1lbnQgRGl2aXNpb24gLSBNaW5pbmcgb3BlcmF0aW9ucyBhbmQgcGVybWl0cycsXG4gICAgICAgICdNU0VTREQnOiAnTWluZXMgU2FmZXR5LCBFbnZpcm9ubWVudCBhbmQgU29jaWFsIERldmVsb3BtZW50IERpdmlzaW9uIC0gU2FmZXR5IGFuZCBlbnZpcm9ubWVudGFsIGNvbXBsaWFuY2UnLFxuICAgICAgICAnR1NEJzogJ0dlb2xvZ2ljYWwgU3VydmV5IERpdmlzaW9uIC0gR2VvbG9naWNhbCByZXNlYXJjaCBhbmQgc3VydmV5cydcbiAgICAgIH07XG5cbiAgICAgIGlmIChkaXZpc2lvbkluZm9bdXNlckRpdmlzaW9uXSkge1xuICAgICAgICBtZXNzYWdlICs9IGBcXG5cXG7wn4+iICoqJHt1c2VyRGl2aXNpb259IERpdmlzaW9uKiogLSAke2RpdmlzaW9uSW5mb1t1c2VyRGl2aXNpb25dfWA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ29udGV4dHVhbCB3ZWxjb21lIGJhc2VkIG9uIGN1cnJlbnQgcGFnZVxuICAgIGlmIChjdXJyZW50UGFnZT8uaW5jbHVkZXMoJy9kb2N1bWVudHMvYWRkJykpIHtcbiAgICAgIG1lc3NhZ2UgKz0gJ1xcblxcbvCfk50gKipDcmVhdGluZyBhIERvY3VtZW50KiogLSBJIGNhbiBndWlkZSB5b3UgdGhyb3VnaCB0aGUgZG9jdW1lbnQgY3JlYXRpb24gcHJvY2VzcywgaGVscCB5b3UgY2hvb3NlIHRoZSByaWdodCBjYXRlZ29yeSBhbmQgYWN0aW9uIHR5cGUsIGFuZCBleHBsYWluIHRoZSB3b3JrZmxvdy4nO1xuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhZ2U/LmluY2x1ZGVzKCcvZG9jdW1lbnRzJykgJiYgY3VycmVudFBhZ2U/LmluY2x1ZGVzKCdmaWx0ZXI9aW5ib3gnKSkge1xuICAgICAgbWVzc2FnZSArPSAnXFxuXFxu8J+TpSAqKkluYm94IE1hbmFnZW1lbnQqKiAtIEkgY2FuIGhlbHAgeW91IHVuZGVyc3RhbmQgZG9jdW1lbnQgc3RhdHVzZXMsIHByb2Nlc3MgcmVjZWl2ZWQgZG9jdW1lbnRzLCBhbmQgbWFuYWdlIHlvdXIgd29ya2Zsb3cgZWZmaWNpZW50bHkuJztcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYWdlPy5pbmNsdWRlcygnL2Rhc2hib2FyZCcpKSB7XG4gICAgICBtZXNzYWdlICs9ICdcXG5cXG7wn5OKICoqRGFzaGJvYXJkIE92ZXJ2aWV3KiogLSBJIGNhbiBleHBsYWluIHRoZSBzdGF0aXN0aWNzLCBoZWxwIHlvdSBuYXZpZ2F0ZSB0byBkaWZmZXJlbnQgc2VjdGlvbnMsIGFuZCBwcm92aWRlIGluc2lnaHRzIGFib3V0IHlvdXIgZG9jdW1lbnQgYWN0aXZpdHkuJztcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYWdlPy5pbmNsdWRlcygnL2FkbWluJykpIHtcbiAgICAgIG1lc3NhZ2UgKz0gJ1xcblxcbvCflKcgKipBZG1pbmlzdHJhdGl2ZSBQYW5lbCoqIC0gSSBjYW4gYXNzaXN0IHdpdGggdXNlciBtYW5hZ2VtZW50LCBzeXN0ZW0gY29uZmlndXJhdGlvbiwgcmVwb3J0cyBnZW5lcmF0aW9uLCBhbmQgYWRtaW5pc3RyYXRpdmUgdGFza3MuJztcbiAgICB9IGVsc2Uge1xuICAgICAgbWVzc2FnZSArPSAnXFxuXFxu8J+OryAqKlJlYWR5IHRvIEhlbHAqKiAtIEFzayBtZSBhYm91dCBhbnkgYXNwZWN0IG9mIHRoZSBkb2N1bWVudCB0cmFja2luZyBzeXN0ZW0hJztcbiAgICB9XG5cbiAgICBtZXNzYWdlICs9ICdcXG5cXG4qKvCfkqEgV2hhdCBJIGNhbiBoZWxwIHlvdSB3aXRoOioqXFxu4oCiIERvY3VtZW50IGNyZWF0aW9uIGFuZCBtYW5hZ2VtZW50XFxu4oCiIFdvcmtmbG93IHByb2Nlc3NlcyBhbmQgc3RhdHVzIHRyYWNraW5nXFxu4oCiIEFkdmFuY2VkIHNlYXJjaCBhbmQgZmlsdGVyaW5nXFxu4oCiIFN5c3RlbSBmZWF0dXJlcyBhbmQgbmF2aWdhdGlvblxcbuKAoiBSb2xlLXNwZWNpZmljIGZ1bmN0aW9ucyBhbmQgcGVybWlzc2lvbnMnO1xuXG4gICAgY29uc3QgYWN0aW9ucyA9IFtcbiAgICAgIHsgdHlwZTogJ2hlbHAnLCBsYWJlbDogJ/CfmoAgUXVpY2sgVG91cicsIGRhdGE6IHsgdG9waWM6ICdnZXR0aW5nX3N0YXJ0ZWQnIH0gfSxcbiAgICAgIHsgdHlwZTogJ25hdmlnYXRlJywgbGFiZWw6ICfwn5OKIERhc2hib2FyZCcsIGRhdGE6IHsgdXJsOiAnL2Rhc2hib2FyZCcgfSB9LFxuICAgICAgeyB0eXBlOiAnaW5mbycsIGxhYmVsOiAn8J+OryBTeXN0ZW0gRmVhdHVyZXMnLCBkYXRhOiB7IHRvcGljOiAnZmVhdHVyZXMnIH0gfVxuICAgIF07XG5cbiAgICAvLyBSb2xlLXNwZWNpZmljIHF1aWNrIHJlcGxpZXNcbiAgICBsZXQgcXVpY2tSZXBsaWVzID0gW107XG4gICAgaWYgKHVzZXJSb2xlID09PSAnQURNSU4nIHx8IHVzZXJSb2xlID09PSAnUkVHSU9OQUxfRElSRUNUT1InKSB7XG4gICAgICBxdWlja1JlcGxpZXMgPSBbXG4gICAgICAgICdVc2VyIG1hbmFnZW1lbnQgaGVscCcsXG4gICAgICAgICdTeXN0ZW0gcmVwb3J0cyBhbmQgYW5hbHl0aWNzJyxcbiAgICAgICAgJ0FkbWluaXN0cmF0aXZlIGZ1bmN0aW9ucycsXG4gICAgICAgICdIb3cgdG8gc2VuZCBkb2N1bWVudHM/J1xuICAgICAgXTtcbiAgICB9IGVsc2UgaWYgKHVzZXJSb2xlID09PSAnRElWSVNJT05fQ0hJRUYnKSB7XG4gICAgICBxdWlja1JlcGxpZXMgPSBbXG4gICAgICAgICdEaXZpc2lvbiB3b3JrZmxvdyBtYW5hZ2VtZW50JyxcbiAgICAgICAgJ1RlYW0gY29vcmRpbmF0aW9uJyxcbiAgICAgICAgJ0hvdyB0byBzZW5kIGRvY3VtZW50cz8nLFxuICAgICAgICAnQ2hlY2sgbXkgaW5ib3gnXG4gICAgICBdO1xuICAgIH0gZWxzZSB7XG4gICAgICBxdWlja1JlcGxpZXMgPSBbXG4gICAgICAgICdIb3cgZG8gSSBzZW5kIGEgZG9jdW1lbnQ/JyxcbiAgICAgICAgJ0NoZWNrIG15IGluYm94JyxcbiAgICAgICAgJ0RvY3VtZW50IHdvcmtmbG93IGhlbHAnLFxuICAgICAgICAnQWR2YW5jZWQgc2VhcmNoIHRpcHMnXG4gICAgICBdO1xuICAgIH1cblxuICAgIHJldHVybiB7IG1lc3NhZ2UsIGFjdGlvbnMsIHF1aWNrUmVwbGllcyB9O1xuICB9XG5cbiAgLyoqXG4gICAqIENsZWFuIHVwIG9sZCBzZXNzaW9ucyAoY2FsbCBwZXJpb2RpY2FsbHkpXG4gICAqL1xuICBjbGVhbnVwT2xkU2Vzc2lvbnMoKTogdm9pZCB7XG4gICAgLy8gSW5jcmVhc2Ugc2Vzc2lvbiBsaWZldGltZSB0byA0IGhvdXJzIHRvIHJlZHVjZSBzZXNzaW9uIGxvc3NcbiAgICBjb25zdCBmb3VySG91cnNBZ28gPSBuZXcgRGF0ZShEYXRlLm5vdygpIC0gNCAqIDYwICogNjAgKiAxMDAwKTtcblxuICAgIGxldCBjbGVhbmVkQ291bnQgPSAwO1xuICAgIGZvciAoY29uc3QgW3Nlc3Npb25JZCwgc2Vzc2lvbl0gb2YgdGhpcy5zZXNzaW9ucy5lbnRyaWVzKCkpIHtcbiAgICAgIGlmIChzZXNzaW9uLnVwZGF0ZWRBdCA8IGZvdXJIb3Vyc0Fnbykge1xuICAgICAgICB0aGlzLnNlc3Npb25zLmRlbGV0ZShzZXNzaW9uSWQpO1xuICAgICAgICBjbGVhbmVkQ291bnQrKztcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoY2xlYW5lZENvdW50ID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYENsZWFuZWQgdXAgJHtjbGVhbmVkQ291bnR9IG9sZCBjaGF0IHNlc3Npb25zYCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIHNlc3Npb24gZXhpc3RzIGFuZCBpcyB2YWxpZFxuICAgKi9cbiAgaGFzVmFsaWRTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gZmFsc2U7XG5cbiAgICAvLyBDaGVjayBpZiBzZXNzaW9uIGlzIG5vdCB0b28gb2xkICg0IGhvdXJzKVxuICAgIGNvbnN0IGZvdXJIb3Vyc0FnbyA9IG5ldyBEYXRlKERhdGUubm93KCkgLSA0ICogNjAgKiA2MCAqIDEwMDApO1xuICAgIHJldHVybiBzZXNzaW9uLnVwZGF0ZWRBdCA+IGZvdXJIb3Vyc0FnbztcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2Vzc2lvbiBzdGF0aXN0aWNzXG4gICAqL1xuICBnZXRTdGF0cygpOiB7XG4gICAgYWN0aXZlU2Vzc2lvbnM6IG51bWJlcjtcbiAgICB0b3RhbE1lc3NhZ2VzOiBudW1iZXI7XG4gICAgYXZlcmFnZVNlc3Npb25MZW5ndGg6IG51bWJlcjtcbiAgfSB7XG4gICAgY29uc3QgYWN0aXZlU2Vzc2lvbnMgPSB0aGlzLnNlc3Npb25zLnNpemU7XG4gICAgbGV0IHRvdGFsTWVzc2FnZXMgPSAwO1xuICAgIGxldCB0b3RhbFNlc3Npb25MZW5ndGggPSAwO1xuXG4gICAgZm9yIChjb25zdCBzZXNzaW9uIG9mIHRoaXMuc2Vzc2lvbnMudmFsdWVzKCkpIHtcbiAgICAgIHRvdGFsTWVzc2FnZXMgKz0gc2Vzc2lvbi5tZXNzYWdlcy5sZW5ndGg7XG4gICAgICB0b3RhbFNlc3Npb25MZW5ndGggKz0gc2Vzc2lvbi5tZXNzYWdlcy5sZW5ndGg7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGFjdGl2ZVNlc3Npb25zLFxuICAgICAgdG90YWxNZXNzYWdlcyxcbiAgICAgIGF2ZXJhZ2VTZXNzaW9uTGVuZ3RoOiBhY3RpdmVTZXNzaW9ucyA+IDAgPyB0b3RhbFNlc3Npb25MZW5ndGggLyBhY3RpdmVTZXNzaW9ucyA6IDBcbiAgICB9O1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBjaGF0Ym90U2VydmljZSA9IG5ldyBDaGF0Ym90U2VydmljZSgpO1xuXG4vLyBBdXRvLWNsZWFudXAgb2xkIHNlc3Npb25zIGV2ZXJ5IDMwIG1pbnV0ZXNcbmlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgeyAvLyBTZXJ2ZXItc2lkZSBvbmx5XG4gIHNldEludGVydmFsKCgpID0+IHtcbiAgICBjaGF0Ym90U2VydmljZS5jbGVhbnVwT2xkU2Vzc2lvbnMoKTtcbiAgfSwgMzAgKiA2MCAqIDEwMDApO1xufVxuIl0sIm5hbWVzIjpbIkNoYXRib3RTZXJ2aWNlIiwiR2VtaW5pQ2hhdGJvdFNlcnZpY2UiLCJzdGFydFNlc3Npb24iLCJ1c2VySWQiLCJ1c2VyQ29udGV4dCIsInNlc3Npb25JZCIsIkRhdGUiLCJub3ciLCJzZXNzaW9uIiwiaWQiLCJtZXNzYWdlcyIsImNvbnRleHQiLCJ1c2VyIiwiY3VycmVudFBhZ2UiLCJzeXN0ZW1TdGF0ZSIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsIndlbGNvbWVNZXNzYWdlIiwiZ2VuZXJhdGVXZWxjb21lTWVzc2FnZSIsInB1c2giLCJyb2xlIiwiY29udGVudCIsIm1lc3NhZ2UiLCJ0aW1lc3RhbXAiLCJhY3Rpb25zIiwicXVpY2tSZXBsaWVzIiwic2Vzc2lvbnMiLCJzZXQiLCJzZW5kTWVzc2FnZSIsInVzZXJNZXNzYWdlIiwiZ2V0IiwiY29uc29sZSIsIndhcm4iLCJ1c2VySWRNYXRjaCIsIm1hdGNoIiwibG9nIiwidG9JU09TdHJpbmciLCJyZWNvdmVyeU1lc3NhZ2UiLCJ0eXBlIiwibGFiZWwiLCJkYXRhIiwidXJsIiwidG9waWMiLCJFcnJvciIsInVzZXJNc2ciLCJjb252ZXJzYXRpb25IaXN0b3J5Iiwic2xpY2UiLCJtYXAiLCJtc2ciLCJhaVJlc3BvbnNlIiwiZ2VuZXJhdGVDaGF0UmVzcG9uc2UiLCJhaUVycm9yIiwiZXJyb3IiLCJhc3Npc3RhbnRNc2ciLCJnZXRTZXNzaW9uIiwidXBkYXRlU2Vzc2lvbkNvbnRleHQiLCJ1cGRhdGVEb2N1bWVudENvbnRleHQiLCJkb2N1bWVudERhdGEiLCJnZXRDb250ZXh0dWFsSGVscCIsImdlbmVyYXRlQ29udGV4dHVhbEhlbHAiLCJ1c2VyUm9sZSIsInJlY2VudEFjdGlvbnMiLCJwZW5kaW5nRG9jdW1lbnRzIiwiY29tbW9uSXNzdWVzIiwiYW5hbHl6ZUludGVudCIsInVzZXJEaXZpc2lvbiIsImRpdmlzaW9uIiwibmFtZSIsImRpdmlzaW9uSW5mbyIsImluY2x1ZGVzIiwiY2xlYW51cE9sZFNlc3Npb25zIiwiZm91ckhvdXJzQWdvIiwiY2xlYW5lZENvdW50IiwiZW50cmllcyIsImRlbGV0ZSIsImhhc1ZhbGlkU2Vzc2lvbiIsImdldFN0YXRzIiwiYWN0aXZlU2Vzc2lvbnMiLCJzaXplIiwidG90YWxNZXNzYWdlcyIsInRvdGFsU2Vzc2lvbkxlbmd0aCIsInZhbHVlcyIsImxlbmd0aCIsImF2ZXJhZ2VTZXNzaW9uTGVuZ3RoIiwiTWFwIiwiY2hhdGJvdFNlcnZpY2UiLCJzZXRJbnRlcnZhbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/chatbot/chatbotService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/mongodb.ts":
/*!*******************************!*\
  !*** ./src/lib/db/mongodb.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isConnectionHealthy: () => (/* binding */ isConnectionHealthy)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _register_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./register-models */ \"(rsc)/./src/lib/db/register-models.ts\");\n\n\nconst MONGODB_URI = process.env.MONGODB_URI || \"mongodb://localhost:27017/document-tracker\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\n// Connection options\nconst connectionOptions = {\n    bufferCommands: false,\n    serverSelectionTimeoutMS: 10000,\n    socketTimeoutMS: 45000,\n    family: 4,\n    // Enable auto reconnect\n    autoIndex: true,\n    autoCreate: true,\n    // Connection health monitoring\n    heartbeatFrequencyMS: 10000\n};\n// Initialize cached connection object\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null,\n    isConnecting: false,\n    lastConnectionAttempt: 0\n};\nif (!global.mongoose) {\n    global.mongoose = cached;\n}\n// Set up connection event listeners\nfunction setupConnectionMonitoring() {\n    // Only set up listeners once\n    if (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.listenerCount(\"connected\") > 0) return;\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"connected\", ()=>{\n        console.log(\"MongoDB connection established successfully\");\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n        console.error(\"MongoDB connection error:\", err);\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n        console.warn(\"MongoDB disconnected. Will attempt to reconnect automatically.\");\n    });\n    // Handle process termination\n    process.on(\"SIGINT\", async ()=>{\n        try {\n            await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n            console.log(\"MongoDB connection closed due to application termination\");\n            process.exit(0);\n        } catch (err) {\n            console.error(\"Error closing MongoDB connection:\", err);\n            process.exit(1);\n        }\n    });\n}\n// Check if connection is healthy\nfunction isConnectionHealthy() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1; // 1 = connected\n}\n// Main connection function\nasync function dbConnect() {\n    // Set up connection monitoring\n    setupConnectionMonitoring();\n    // If we already have a connection and it's healthy, return it\n    if (cached.conn && isConnectionHealthy()) {\n        console.log(\"Using existing MongoDB connection\");\n        return cached.conn;\n    }\n    console.log(\"No healthy MongoDB connection found, creating a new one\");\n    // If we're already trying to connect, wait for that promise\n    if (cached.isConnecting && cached.promise) {\n        try {\n            cached.conn = await cached.promise;\n            return cached.conn;\n        } catch (error) {\n            // If the current connection attempt fails, we'll try again below\n            console.error(\"Ongoing connection attempt failed:\", error);\n        }\n    }\n    // Prevent connection attempts in rapid succession (throttle to once per 5 seconds)\n    const now = Date.now();\n    const minTimeBetweenAttempts = 5000; // 5 seconds\n    if (now - cached.lastConnectionAttempt < minTimeBetweenAttempts) {\n        console.warn(\"Connection attempt throttled. Waiting before retrying...\");\n        await new Promise((resolve)=>setTimeout(resolve, minTimeBetweenAttempts));\n    }\n    // Start a new connection attempt\n    cached.isConnecting = true;\n    cached.lastConnectionAttempt = Date.now();\n    cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, connectionOptions).then((mongoose)=>{\n        console.log(\"MongoDB connected successfully\");\n        cached.isConnecting = false;\n        // Register models after successful connection\n        (0,_register_models__WEBPACK_IMPORTED_MODULE_1__.registerModels)();\n        return mongoose;\n    }).catch((error)=>{\n        console.error(\"MongoDB connection error:\", error);\n        cached.isConnecting = false;\n        cached.promise = null; // Reset the promise on error\n        throw error;\n    });\n    try {\n        cached.conn = await cached.promise;\n        return cached.conn;\n    } catch (error) {\n        console.error(\"Failed to connect to MongoDB:\", error);\n        // Implement exponential backoff for retries in production\n        if (false) {}\n        throw error;\n    }\n}\n// Export the connection function and health check\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/register-models.ts":
/*!***************************************!*\
  !*** ./src/lib/db/register-models.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerModels: () => (/* binding */ registerModels)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\n// Define the Document Journey Schema\nconst DocumentJourneySchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\n// Define the Document Schema\nconst DocumentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly with case-insensitivity\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Register models\nfunction registerModels() {\n    // Only register models if they haven't been registered yet\n    if (!(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Document) {\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n        console.log(\"Document model registered\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/register-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/core/geminiClient.ts":
/*!*********************************************!*\
  !*** ./src/lib/gemini/core/geminiClient.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiClient: () => (/* binding */ GeminiClient),\n/* harmony export */   geminiClient: () => (/* binding */ geminiClient)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/gemini/core/types.ts\");\n/**\n * Core Gemini API client\n * Handles direct communication with Google's Gemini API\n */ \nclass GeminiClient {\n    constructor(config = {}){\n        this.apiKey = _types__WEBPACK_IMPORTED_MODULE_0__.GEMINI_CONFIG.API_KEY;\n        this.apiUrl = _types__WEBPACK_IMPORTED_MODULE_0__.GEMINI_CONFIG.API_URL;\n        this.config = {\n            ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_AI_CONFIG,\n            ...config\n        };\n    }\n    /**\n   * Check if Gemini API is available\n   */ isAvailable() {\n        return !!this.apiKey;\n    }\n    /**\n   * Call Gemini API with the provided prompt\n   * @param prompt The prompt to send to Gemini\n   * @returns The generated response\n   */ async generateContent(prompt) {\n        if (!this.apiKey) {\n            throw new Error(\"Gemini API key not configured\");\n        }\n        const requestBody = {\n            contents: [\n                {\n                    parts: [\n                        {\n                            text: prompt\n                        }\n                    ]\n                }\n            ],\n            generationConfig: this.config\n        };\n        const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        if (!data.candidates || data.candidates.length === 0) {\n            throw new Error(\"No response from Gemini API\");\n        }\n        return data.candidates[0].content.parts[0].text;\n    }\n    /**\n   * Generate content with retry logic\n   * @param prompt The prompt to send\n   * @param maxRetries Maximum number of retries\n   * @returns The generated response\n   */ async generateContentWithRetry(prompt, maxRetries = 3) {\n        let lastError = null;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                return await this.generateContent(prompt);\n            } catch (error) {\n                lastError = error;\n                console.warn(`Gemini API attempt ${attempt} failed:`, error);\n                if (attempt < maxRetries) {\n                    // Wait before retrying (exponential backoff)\n                    const delay = Math.pow(2, attempt) * 1000;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        throw lastError || new Error(\"All Gemini API attempts failed\");\n    }\n    /**\n   * Update configuration\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n    }\n    /**\n   * Get current configuration\n   */ getConfig() {\n        return {\n            ...this.config\n        };\n    }\n}\n/**\n * Default Gemini client instance\n */ const geminiClient = new GeminiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/core/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/core/types.ts":
/*!**************************************!*\
  !*** ./src/lib/gemini/core/types.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AI_CONFIG: () => (/* binding */ DEFAULT_AI_CONFIG),\n/* harmony export */   GEMINI_CONFIG: () => (/* binding */ GEMINI_CONFIG)\n/* harmony export */ });\n/**\n * Core types and interfaces for Gemini AI service\n */ /**\n * Interface for Gemini API request\n */ /**\n * Default AI configuration\n */ const DEFAULT_AI_CONFIG = {\n    temperature: 0.7,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 2048\n};\n/**\n * API endpoints configuration\n */ const GEMINI_CONFIG = {\n    API_URL: \"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent\",\n    API_KEY: process.env.GEMINI_API_KEY\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/core/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/fallbacks/promptTemplates.ts":
/*!*****************************************************!*\
  !*** ./src/lib/gemini/fallbacks/promptTemplates.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PromptTemplates: () => (/* binding */ PromptTemplates)\n/* harmony export */ });\n/**\n * Prompt Templates\n * Centralized prompt templates for AI interactions\n */ class PromptTemplates {\n    /**\n   * Create feedback analysis prompt\n   */ static createFeedbackPrompt(userFeedback, systemContext) {\n        return `\nYou are an AI assistant for a Document Tracking System administrator.\nA user has submitted the following feedback or bug report:\n\n\"${userFeedback}\"\n\n${systemContext ? `\\nAdditional system context:\\n${systemContext}\\n` : \"\"}\n\nPlease analyze this feedback and provide a structured response with the following sections:\n\n## Summary\nA concise description of the issue or suggestion.\n\n## Steps to Reproduce (for bugs) or Implementation Considerations (for features)\nClear steps to reproduce the issue or key considerations for implementing the feature.\n\n## Potential Fixes or Development Steps\nSpecific actions that can be taken to address the feedback.\n\n## Augment AI Prompt\nCreate a prompt that can be used with Augment AI to help implement the solution. The prompt should be specific and include:\n- What code to look for\n- What issues to identify\n- What changes to make\n- Any specific requirements or constraints\n\n## Priority\nIndicate the priority level (Low, Medium, High) and recommended timeline for addressing this feedback.\n\nFormat your response in Markdown for better readability.\n`;\n    }\n    /**\n   * Create smart notification prompt\n   */ static createNotificationPrompt(document, user, context) {\n        return `\nYou are an AI assistant for a Document Tracking System that generates intelligent notifications.\n\nDOCUMENT INFORMATION:\n- Title: ${document.title}\n- Category: ${document.category}\n- Status: ${document.status}\n- Created: ${document.createdAt}\n- Action Required: ${document.action || \"None specified\"}\n\nUSER INFORMATION:\n- Name: ${user.name}\n- Division: ${user.division}\n- Role: ${user.role}\n\nCONTEXT:\n- Current Workload: ${context.workload || \"Unknown\"} documents\n- Urgency Level: ${context.urgency || \"medium\"}\n- Deadline: ${context.deadline ? new Date(context.deadline).toLocaleDateString() : \"Not specified\"}\n- Related Documents: ${context.relatedDocuments || 0}\n\nGenerate a smart notification that includes:\n\n1. A personalized message that considers the user's workload and role\n2. Priority level (low, medium, high, critical) based on urgency and context\n3. 2-3 suggested actions the user can take\n4. Optimal timing recommendation for when to send this notification\n\nFormat your response as JSON:\n{\n  \"message\": \"Personalized notification message\",\n  \"priority\": \"medium\",\n  \"suggestedActions\": [\"Action 1\", \"Action 2\", \"Action 3\"],\n  \"optimalTiming\": \"Timing recommendation\"\n}\n\nMake the message concise but informative, and ensure suggested actions are specific and actionable.\n`;\n    }\n    /**\n   * Create behavior analysis prompt\n   */ static createBehaviorAnalysisPrompt(userId, userActivity) {\n        const loginTimesStr = userActivity.loginTimes?.map((time)=>time.toISOString()).join(\", \") || \"No data\";\n        return `\nYou are an AI assistant analyzing user behavior patterns for a Document Tracking System.\n\nUSER ID: ${userId}\n\nACTIVITY DATA:\n- Login Times (last 30 days): ${loginTimesStr}\n- Document Types Handled: ${userActivity.documentTypes?.join(\", \") || \"No data\"}\n- Average Response Time: ${userActivity.averageResponseTime || \"Unknown\"} hours\n- Response Patterns: ${JSON.stringify(userActivity.responsePatterns || {})}\n\nAnalyze this data and provide recommendations for optimal notification timing and user engagement.\n\nFormat your response as JSON:\n{\n  \"optimalHours\": [9, 10, 14, 15],\n  \"preferredDays\": [\"Monday\", \"Tuesday\", \"Wednesday\"],\n  \"responsePatterns\": {\n    \"fastResponse\": \"Morning hours\",\n    \"slowResponse\": \"Late afternoon\",\n    \"peakActivity\": \"10:00-11:00 AM\"\n  },\n  \"recommendations\": [\n    \"Send urgent notifications during peak hours\",\n    \"Batch non-urgent notifications\",\n    \"Avoid notifications during lunch hours\"\n  ]\n}\n\nConsider:\n1. Most active hours based on login patterns\n2. Days with highest activity\n3. Response time patterns\n4. Document handling preferences\n5. Optimal notification frequency\n`;\n    }\n    /**\n   * Create reminder scheduling prompt\n   */ static createReminderPrompt(document, user, deadline) {\n        return `\nYou are an AI assistant for a Document Tracking System that creates intelligent reminder schedules.\n\nDOCUMENT INFORMATION:\n- Title: ${document.title}\n- Category: ${document.category}\n- Status: ${document.status}\n- Created: ${document.createdAt}\n- Action Required: ${document.action || \"None specified\"}\n\nUSER INFORMATION:\n- Name: ${user.name}\n- Division: ${user.division}\n- Role: ${user.role}\n\nDEADLINE: ${deadline ? deadline.toISOString() : \"Not specified\"}\n\nCreate an intelligent reminder schedule that includes:\n\n1. A series of reminders leading up to the deadline\n2. Different reminder types (gentle, urgent, critical)\n3. Escalation plan if no response\n4. Optimal timing for each reminder\n\nFormat your response as JSON:\n{\n  \"reminders\": [\n    {\n      \"scheduledFor\": \"2024-01-15T09:00:00Z\",\n      \"message\": \"Gentle reminder about document\",\n      \"type\": \"gentle\",\n      \"escalate\": false\n    },\n    {\n      \"scheduledFor\": \"2024-01-16T14:00:00Z\",\n      \"message\": \"Urgent reminder - deadline approaching\",\n      \"type\": \"urgent\",\n      \"escalate\": true\n    }\n  ],\n  \"escalationPlan\": {\n    \"escalateAfter\": \"24 hours\",\n    \"escalateTo\": \"supervisor\",\n    \"escalationMessage\": \"Document requires immediate attention\"\n  }\n}\n\nConsider:\n- Document urgency and category\n- User's role and responsibilities\n- Time until deadline\n- Appropriate escalation levels\n`;\n    }\n    /**\n   * Create chat prompt\n   */ static createChatPrompt(message, context, conversationHistory = []) {\n        const historyText = conversationHistory.slice(-5) // Last 5 messages for context\n        .map((msg)=>`${msg.role}: ${msg.content}`).join(\"\\n\");\n        return `\nYou are MGB Bot, an AI assistant for the MGB Document Tracking System.\n\nCONTEXT:\n- Current Page: ${context.currentPage || \"Unknown\"}\n- User Role: ${context.userRole || \"Unknown\"}\n- User Division: ${context.userDivision || \"Unknown\"}\n- Pending Documents: ${context.pendingDocuments || 0}\n\nCONVERSATION HISTORY:\n${historyText}\n\nUSER MESSAGE: ${message}\n\nProvide a helpful, concise response that:\n1. Addresses the user's question or request\n2. Considers their role and current context\n3. Offers specific, actionable guidance\n4. Uses a friendly, professional tone\n5. Stays focused on document tracking system features\n\nIf the user asks about:\n- Document creation: Guide them through the process\n- Document status: Explain the workflow and statuses\n- Navigation: Help them find the right page or feature\n- Troubleshooting: Provide step-by-step solutions\n- System features: Explain functionality clearly\n\nKeep responses under 200 words and be conversational.\n`;\n    }\n    /**\n   * Create intent analysis prompt\n   */ static createIntentAnalysisPrompt(message) {\n        return `\nAnalyze the user's intent from this message: \"${message}\"\n\nClassify the intent and extract entities. Respond in JSON format:\n{\n  \"intent\": \"intent_name\",\n  \"confidence\": 0.85,\n  \"entities\": [\n    {\"type\": \"entity_type\", \"value\": \"entity_value\"}\n  ],\n  \"suggestions\": [\"suggestion1\", \"suggestion2\"]\n}\n\nCommon intents:\n- document_create, document_search, document_status\n- navigation_help, feature_explanation, troubleshooting\n- greeting, goodbye, help_request\n\nCommon entities:\n- document_type: memo, letter, report, notice, directive\n- division: ord, fad, mmd, msesdd, gsd\n- document_tracking_number: mgbr2-yyyy-nnnn-nnnn format\n- system_feature: dashboard, inbox, notifications, settings\n`;\n    }\n    /**\n   * Create contextual help prompt\n   */ static createContextualHelpPrompt(context) {\n        return `\nGenerate contextual help for a user in the Document Tracking System.\n\nCONTEXT:\n- Current Page: ${context.currentPage || \"Unknown\"}\n- User Role: ${context.userRole || \"Unknown\"}\n- User Division: ${context.userDivision || \"Unknown\"}\n- Pending Documents: ${context.pendingDocuments || 0}\n\nProvide help in JSON format:\n{\n  \"helpMessage\": \"Contextual help message based on current page and user state\",\n  \"quickActions\": [\n    {\"label\": \"Action Label\", \"action\": \"action_id\", \"description\": \"What this action does\"}\n  ],\n  \"tutorials\": [\n    {\"title\": \"Tutorial Title\", \"steps\": [\"Step 1\", \"Step 2\", \"Step 3\"]}\n  ]\n}\n\nConsider:\n- What the user is currently trying to do\n- Their role and permissions\n- Common tasks for their current page\n- Helpful shortcuts and tips\n- Step-by-step guidance for complex tasks\n`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/fallbacks/promptTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BehaviorAnalysisService: () => (/* reexport safe */ _services_behaviorService__WEBPACK_IMPORTED_MODULE_4__.BehaviorAnalysisService),\n/* harmony export */   ChatbotService: () => (/* reexport safe */ _services_chatbotService__WEBPACK_IMPORTED_MODULE_6__.ChatbotService),\n/* harmony export */   DEFAULT_AI_CONFIG: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_AI_CONFIG),\n/* harmony export */   FeedbackService: () => (/* reexport safe */ _services_feedbackService__WEBPACK_IMPORTED_MODULE_2__.FeedbackService),\n/* harmony export */   GEMINI_CONFIG: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.GEMINI_CONFIG),\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService),\n/* harmony export */   PromptTemplates: () => (/* reexport safe */ _fallbacks_promptTemplates__WEBPACK_IMPORTED_MODULE_7__.PromptTemplates),\n/* harmony export */   ReminderService: () => (/* reexport safe */ _services_reminderService__WEBPACK_IMPORTED_MODULE_5__.ReminderService),\n/* harmony export */   SmartNotificationService: () => (/* reexport safe */ _services_notificationService__WEBPACK_IMPORTED_MODULE_3__.SmartNotificationService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   geminiClient: () => (/* reexport safe */ _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/* harmony import */ var _core_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core/types */ \"(rsc)/./src/lib/gemini/core/types.ts\");\n/* harmony import */ var _services_feedbackService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/feedbackService */ \"(rsc)/./src/lib/gemini/services/feedbackService.ts\");\n/* harmony import */ var _services_notificationService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/notificationService */ \"(rsc)/./src/lib/gemini/services/notificationService.ts\");\n/* harmony import */ var _services_behaviorService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/behaviorService */ \"(rsc)/./src/lib/gemini/services/behaviorService.ts\");\n/* harmony import */ var _services_reminderService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/reminderService */ \"(rsc)/./src/lib/gemini/services/reminderService.ts\");\n/* harmony import */ var _services_chatbotService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./services/chatbotService */ \"(rsc)/./src/lib/gemini/services/chatbotService.ts\");\n/* harmony import */ var _fallbacks_promptTemplates__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./fallbacks/promptTemplates */ \"(rsc)/./src/lib/gemini/fallbacks/promptTemplates.ts\");\n/**\n * Gemini AI Service - Main Export File\n * Provides a clean interface to all Gemini AI functionality\n */ // Core exports\n\n\n// Service exports\n\n\n\n\n\n// Utility exports\n\n/**\n * Main Gemini Service Class\n * Provides a unified interface to all AI services\n */ class GeminiService {\n    /**\n   * Generate feedback suggestion\n   */ static async generateSuggestion(userFeedback, systemContext) {\n        return FeedbackService.generateSuggestion(userFeedback, systemContext);\n    }\n    /**\n   * Generate smart notification\n   */ static async generateSmartNotification(document, user, context) {\n        return SmartNotificationService.generateSmartNotification(document, user, context);\n    }\n    /**\n   * Analyze user behavior\n   */ static async analyzeUserBehavior(userId, userActivity) {\n        return BehaviorAnalysisService.analyzeUserBehavior(userId, userActivity);\n    }\n    /**\n   * Generate reminder schedule\n   */ static async generateReminderSchedule(document, user, deadline) {\n        return ReminderService.generateReminderSchedule(document, user, deadline);\n    }\n    /**\n   * Generate chat response\n   */ static async generateChatResponse(message, context, conversationHistory = []) {\n        return ChatbotService.generateChatResponse(message, context, conversationHistory);\n    }\n    /**\n   * Analyze user intent\n   */ static async analyzeIntent(message) {\n        return ChatbotService.analyzeIntent(message);\n    }\n    /**\n   * Generate contextual help\n   */ static async generateContextualHelp(context) {\n        return ChatbotService.generateContextualHelp(context);\n    }\n    /**\n   * Check if Gemini API is available\n   */ static isAvailable() {\n        return geminiClient.isAvailable();\n    }\n}\n// Default export for backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GeminiService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/services/behaviorService.ts":
/*!****************************************************!*\
  !*** ./src/lib/gemini/services/behaviorService.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BehaviorAnalysisService: () => (/* binding */ BehaviorAnalysisService)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/**\n * User Behavior Analysis Service\n * Handles AI-powered user behavior pattern analysis\n */ \nclass BehaviorAnalysisService {\n    /**\n   * Analyze user behavior patterns to optimize notification timing\n   * @param userId User ID\n   * @param userActivity Recent user activity data\n   * @returns Optimal notification timing recommendations\n   */ static async analyzeUserBehavior(userId, userActivity) {\n        try {\n            const prompt = this.createBehaviorAnalysisPrompt(userId, userActivity);\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return this.parseBehaviorAnalysis(response);\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for behavior analysis, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local analysis\n            return this.generateLocalBehaviorAnalysis(userActivity);\n        } catch (error) {\n            console.error(\"Error analyzing user behavior:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create prompt for behavior analysis\n   */ static createBehaviorAnalysisPrompt(userId, userActivity) {\n        const loginTimesStr = userActivity.loginTimes.map((time)=>time.toISOString()).join(\", \");\n        return `\nYou are an AI assistant analyzing user behavior patterns for a Document Tracking System.\n\nUSER ID: ${userId}\n\nACTIVITY DATA:\n- Login Times (last 30 days): ${loginTimesStr}\n- Document Types Handled: ${userActivity.documentTypes.join(\", \")}\n- Average Response Time: ${userActivity.averageResponseTime} hours\n- Response Patterns: ${JSON.stringify(userActivity.responsePatterns)}\n\nAnalyze this data and provide recommendations for optimal notification timing and user engagement.\n\nFormat your response as JSON:\n{\n  \"optimalHours\": [9, 10, 14, 15],\n  \"preferredDays\": [\"Monday\", \"Tuesday\", \"Wednesday\"],\n  \"responsePatterns\": {\n    \"fastResponse\": \"Morning hours\",\n    \"slowResponse\": \"Late afternoon\",\n    \"peakActivity\": \"10:00-11:00 AM\"\n  },\n  \"recommendations\": [\n    \"Send urgent notifications during peak hours\",\n    \"Batch non-urgent notifications\",\n    \"Avoid notifications during lunch hours\"\n  ]\n}\n\nConsider:\n1. Most active hours based on login patterns\n2. Days with highest activity\n3. Response time patterns\n4. Document handling preferences\n5. Optimal notification frequency\n`;\n    }\n    /**\n   * Parse behavior analysis response from Gemini API\n   */ static parseBehaviorAnalysis(response) {\n        try {\n            const parsed = JSON.parse(response);\n            return {\n                optimalHours: parsed.optimalHours || [\n                    9,\n                    10,\n                    14,\n                    15\n                ],\n                preferredDays: parsed.preferredDays || [\n                    \"Monday\",\n                    \"Tuesday\",\n                    \"Wednesday\",\n                    \"Thursday\",\n                    \"Friday\"\n                ],\n                responsePatterns: parsed.responsePatterns || {},\n                recommendations: parsed.recommendations || [\n                    \"Send notifications during business hours\"\n                ]\n            };\n        } catch (error) {\n            return this.extractBehaviorFromText(response);\n        }\n    }\n    /**\n   * Extract behavior analysis from text response\n   */ static extractBehaviorFromText(text) {\n        const lines = text.split(\"\\n\").filter((line)=>line.trim());\n        const optimalHours = [];\n        const preferredDays = [];\n        const recommendations = [];\n        const responsePatterns = {};\n        for (const line of lines){\n            const lowerLine = line.toLowerCase();\n            if (lowerLine.includes(\"hour\") && /\\d+/.test(line)) {\n                const hours = line.match(/\\d+/g);\n                if (hours) {\n                    hours.forEach((hour)=>{\n                        const h = parseInt(hour);\n                        if (h >= 0 && h <= 23 && !optimalHours.includes(h)) {\n                            optimalHours.push(h);\n                        }\n                    });\n                }\n            }\n            if (lowerLine.includes(\"day\") || lowerLine.includes(\"monday\") || lowerLine.includes(\"tuesday\")) {\n                const days = [\n                    \"monday\",\n                    \"tuesday\",\n                    \"wednesday\",\n                    \"thursday\",\n                    \"friday\",\n                    \"saturday\",\n                    \"sunday\"\n                ];\n                days.forEach((day)=>{\n                    if (lowerLine.includes(day) && !preferredDays.includes(day)) {\n                        preferredDays.push(day.charAt(0).toUpperCase() + day.slice(1));\n                    }\n                });\n            }\n            if (lowerLine.includes(\"recommend\") || lowerLine.includes(\"suggest\")) {\n                const recommendation = line.replace(/^[-*•]\\s*/, \"\").trim();\n                if (recommendation && recommendations.length < 5) {\n                    recommendations.push(recommendation);\n                }\n            }\n        }\n        // Set defaults if nothing was extracted\n        if (optimalHours.length === 0) {\n            optimalHours.push(9, 10, 14, 15);\n        }\n        if (preferredDays.length === 0) {\n            preferredDays.push(\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\");\n        }\n        if (recommendations.length === 0) {\n            recommendations.push(\"Send notifications during business hours\");\n        }\n        return {\n            optimalHours,\n            preferredDays,\n            responsePatterns,\n            recommendations\n        };\n    }\n    /**\n   * Generate local behavior analysis fallback\n   */ static generateLocalBehaviorAnalysis(userActivity) {\n        const optimalHours = [];\n        const preferredDays = [];\n        const recommendations = [];\n        // Analyze login times to find patterns\n        const hourCounts = {};\n        const dayCounts = {};\n        userActivity.loginTimes.forEach((loginTime)=>{\n            const hour = loginTime.getHours();\n            const day = loginTime.toLocaleDateString(\"en-US\", {\n                weekday: \"long\"\n            });\n            hourCounts[hour] = (hourCounts[hour] || 0) + 1;\n            dayCounts[day] = (dayCounts[day] || 0) + 1;\n        });\n        // Find top 4 hours with most activity\n        const sortedHours = Object.entries(hourCounts).sort(([, a], [, b])=>b - a).slice(0, 4).map(([hour])=>parseInt(hour));\n        optimalHours.push(...sortedHours);\n        // Find top days with most activity\n        const sortedDays = Object.entries(dayCounts).sort(([, a], [, b])=>b - a).slice(0, 5).map(([day])=>day);\n        preferredDays.push(...sortedDays);\n        // Generate recommendations based on patterns\n        if (userActivity.averageResponseTime < 2) {\n            recommendations.push(\"User responds quickly - can send urgent notifications anytime\");\n        } else if (userActivity.averageResponseTime > 8) {\n            recommendations.push(\"User has slower response times - send reminders for urgent items\");\n        }\n        if (optimalHours.some((hour)=>hour >= 6 && hour <= 9)) {\n            recommendations.push(\"User is active in early morning - good time for daily summaries\");\n        }\n        if (optimalHours.some((hour)=>hour >= 13 && hour <= 15)) {\n            recommendations.push(\"User is active in early afternoon - suitable for follow-ups\");\n        }\n        if (preferredDays.includes(\"Monday\") && preferredDays.includes(\"Friday\")) {\n            recommendations.push(\"User is active on Mondays and Fridays - good for weekly updates\");\n        }\n        // Default recommendations if none generated\n        if (recommendations.length === 0) {\n            recommendations.push(\"Send notifications during business hours (9 AM - 5 PM)\", \"Avoid sending notifications on weekends\", \"Bundle non-urgent notifications to reduce interruptions\");\n        }\n        const responsePatterns = {\n            averageResponseTime: `${userActivity.averageResponseTime} hours`,\n            peakActivityHours: optimalHours.join(\", \"),\n            mostActiveDays: preferredDays.slice(0, 3).join(\", \"),\n            documentPreferences: userActivity.documentTypes.slice(0, 3).join(\", \")\n        };\n        return {\n            optimalHours: optimalHours.length > 0 ? optimalHours : [\n                9,\n                10,\n                14,\n                15\n            ],\n            preferredDays: preferredDays.length > 0 ? preferredDays : [\n                \"Monday\",\n                \"Tuesday\",\n                \"Wednesday\",\n                \"Thursday\",\n                \"Friday\"\n            ],\n            responsePatterns,\n            recommendations\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS9zZXJ2aWNlcy9iZWhhdmlvclNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFbUQ7QUFHN0MsTUFBTUM7SUFDWDs7Ozs7R0FLQyxHQUNELGFBQWFDLG9CQUNYQyxNQUFjLEVBQ2RDLFlBQThCLEVBQ0c7UUFDakMsSUFBSTtZQUNGLE1BQU1DLFNBQVMsSUFBSSxDQUFDQyw0QkFBNEIsQ0FBQ0gsUUFBUUM7WUFFekQsSUFBSUosNERBQVlBLENBQUNPLFdBQVcsSUFBSTtnQkFDOUIsSUFBSTtvQkFDRixNQUFNQyxXQUFXLE1BQU1SLDREQUFZQSxDQUFDUyxlQUFlLENBQUNKO29CQUNwRCxPQUFPLElBQUksQ0FBQ0sscUJBQXFCLENBQUNGO2dCQUNwQyxFQUFFLE9BQU9HLFVBQVU7b0JBQ2pCQyxRQUFRQyxJQUFJLENBQUMsNERBQTRERjtnQkFDM0U7WUFDRjtZQUVBLDZCQUE2QjtZQUM3QixPQUFPLElBQUksQ0FBQ0csNkJBQTZCLENBQUNWO1FBQzVDLEVBQUUsT0FBT1csT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE9BQWVULDZCQUE2QkgsTUFBYyxFQUFFQyxZQUE4QixFQUFVO1FBQ2xHLE1BQU1ZLGdCQUFnQlosYUFBYWEsVUFBVSxDQUMxQ0MsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxXQUFXLElBQzVCQyxJQUFJLENBQUM7UUFFUixPQUFPLENBQUM7OztTQUdILEVBQUVsQixPQUFPOzs7OEJBR1ksRUFBRWEsY0FBYzswQkFDcEIsRUFBRVosYUFBYWtCLGFBQWEsQ0FBQ0QsSUFBSSxDQUFDLE1BQU07eUJBQ3pDLEVBQUVqQixhQUFhbUIsbUJBQW1CLENBQUM7cUJBQ3ZDLEVBQUVDLEtBQUtDLFNBQVMsQ0FBQ3JCLGFBQWFzQixnQkFBZ0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUEwQnJFLENBQUM7SUFDQztJQUVBOztHQUVDLEdBQ0QsT0FBZWhCLHNCQUFzQkYsUUFBZ0IsRUFBMEI7UUFDN0UsSUFBSTtZQUNGLE1BQU1tQixTQUFTSCxLQUFLSSxLQUFLLENBQUNwQjtZQUUxQixPQUFPO2dCQUNMcUIsY0FBY0YsT0FBT0UsWUFBWSxJQUFJO29CQUFDO29CQUFHO29CQUFJO29CQUFJO2lCQUFHO2dCQUNwREMsZUFBZUgsT0FBT0csYUFBYSxJQUFJO29CQUFDO29CQUFVO29CQUFXO29CQUFhO29CQUFZO2lCQUFTO2dCQUMvRkosa0JBQWtCQyxPQUFPRCxnQkFBZ0IsSUFBSSxDQUFDO2dCQUM5Q0ssaUJBQWlCSixPQUFPSSxlQUFlLElBQUk7b0JBQUM7aUJBQTJDO1lBQ3pGO1FBQ0YsRUFBRSxPQUFPaEIsT0FBTztZQUNkLE9BQU8sSUFBSSxDQUFDaUIsdUJBQXVCLENBQUN4QjtRQUN0QztJQUNGO0lBRUE7O0dBRUMsR0FDRCxPQUFld0Isd0JBQXdCQyxJQUFZLEVBQTBCO1FBQzNFLE1BQU1DLFFBQVFELEtBQUtFLEtBQUssQ0FBQyxNQUFNQyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLElBQUk7UUFFdkQsTUFBTVQsZUFBeUIsRUFBRTtRQUNqQyxNQUFNQyxnQkFBMEIsRUFBRTtRQUNsQyxNQUFNQyxrQkFBNEIsRUFBRTtRQUNwQyxNQUFNTCxtQkFBd0IsQ0FBQztRQUUvQixLQUFLLE1BQU1XLFFBQVFILE1BQU87WUFDeEIsTUFBTUssWUFBWUYsS0FBS0csV0FBVztZQUVsQyxJQUFJRCxVQUFVRSxRQUFRLENBQUMsV0FBVyxNQUFNQyxJQUFJLENBQUNMLE9BQU87Z0JBQ2xELE1BQU1NLFFBQVFOLEtBQUtPLEtBQUssQ0FBQztnQkFDekIsSUFBSUQsT0FBTztvQkFDVEEsTUFBTUUsT0FBTyxDQUFDQyxDQUFBQTt3QkFDWixNQUFNQyxJQUFJQyxTQUFTRjt3QkFDbkIsSUFBSUMsS0FBSyxLQUFLQSxLQUFLLE1BQU0sQ0FBQ2xCLGFBQWFZLFFBQVEsQ0FBQ00sSUFBSTs0QkFDbERsQixhQUFhb0IsSUFBSSxDQUFDRjt3QkFDcEI7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlSLFVBQVVFLFFBQVEsQ0FBQyxVQUFVRixVQUFVRSxRQUFRLENBQUMsYUFBYUYsVUFBVUUsUUFBUSxDQUFDLFlBQVk7Z0JBQzlGLE1BQU1TLE9BQU87b0JBQUM7b0JBQVU7b0JBQVc7b0JBQWE7b0JBQVk7b0JBQVU7b0JBQVk7aUJBQVM7Z0JBQzNGQSxLQUFLTCxPQUFPLENBQUNNLENBQUFBO29CQUNYLElBQUlaLFVBQVVFLFFBQVEsQ0FBQ1UsUUFBUSxDQUFDckIsY0FBY1csUUFBUSxDQUFDVSxNQUFNO3dCQUMzRHJCLGNBQWNtQixJQUFJLENBQUNFLElBQUlDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLElBQUlHLEtBQUssQ0FBQztvQkFDN0Q7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlmLFVBQVVFLFFBQVEsQ0FBQyxnQkFBZ0JGLFVBQVVFLFFBQVEsQ0FBQyxZQUFZO2dCQUNwRSxNQUFNYyxpQkFBaUJsQixLQUFLbUIsT0FBTyxDQUFDLGFBQWEsSUFBSWxCLElBQUk7Z0JBQ3pELElBQUlpQixrQkFBa0J4QixnQkFBZ0IwQixNQUFNLEdBQUcsR0FBRztvQkFDaEQxQixnQkFBZ0JrQixJQUFJLENBQUNNO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQSx3Q0FBd0M7UUFDeEMsSUFBSTFCLGFBQWE0QixNQUFNLEtBQUssR0FBRztZQUM3QjVCLGFBQWFvQixJQUFJLENBQUMsR0FBRyxJQUFJLElBQUk7UUFDL0I7UUFFQSxJQUFJbkIsY0FBYzJCLE1BQU0sS0FBSyxHQUFHO1lBQzlCM0IsY0FBY21CLElBQUksQ0FBQyxVQUFVLFdBQVcsYUFBYSxZQUFZO1FBQ25FO1FBRUEsSUFBSWxCLGdCQUFnQjBCLE1BQU0sS0FBSyxHQUFHO1lBQ2hDMUIsZ0JBQWdCa0IsSUFBSSxDQUFDO1FBQ3ZCO1FBRUEsT0FBTztZQUNMcEI7WUFDQUM7WUFDQUo7WUFDQUs7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxPQUFlakIsOEJBQThCVixZQUE4QixFQUEwQjtRQUNuRyxNQUFNeUIsZUFBeUIsRUFBRTtRQUNqQyxNQUFNQyxnQkFBMEIsRUFBRTtRQUNsQyxNQUFNQyxrQkFBNEIsRUFBRTtRQUVwQyx1Q0FBdUM7UUFDdkMsTUFBTTJCLGFBQXFDLENBQUM7UUFDNUMsTUFBTUMsWUFBb0MsQ0FBQztRQUUzQ3ZELGFBQWFhLFVBQVUsQ0FBQzRCLE9BQU8sQ0FBQ2UsQ0FBQUE7WUFDOUIsTUFBTWQsT0FBT2MsVUFBVUMsUUFBUTtZQUMvQixNQUFNVixNQUFNUyxVQUFVRSxrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFQyxTQUFTO1lBQU87WUFFcEVMLFVBQVUsQ0FBQ1osS0FBSyxHQUFHLENBQUNZLFVBQVUsQ0FBQ1osS0FBSyxJQUFJLEtBQUs7WUFDN0NhLFNBQVMsQ0FBQ1IsSUFBSSxHQUFHLENBQUNRLFNBQVMsQ0FBQ1IsSUFBSSxJQUFJLEtBQUs7UUFDM0M7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTWEsY0FBY0MsT0FBT0MsT0FBTyxDQUFDUixZQUNoQ1MsSUFBSSxDQUFDLENBQUMsR0FBRUMsRUFBRSxFQUFFLEdBQUVDLEVBQUUsR0FBS0EsSUFBSUQsR0FDekJkLEtBQUssQ0FBQyxHQUFHLEdBQ1RwQyxHQUFHLENBQUMsQ0FBQyxDQUFDNEIsS0FBSyxHQUFLRSxTQUFTRjtRQUU1QmpCLGFBQWFvQixJQUFJLElBQUllO1FBRXJCLG1DQUFtQztRQUNuQyxNQUFNTSxhQUFhTCxPQUFPQyxPQUFPLENBQUNQLFdBQy9CUSxJQUFJLENBQUMsQ0FBQyxHQUFFQyxFQUFFLEVBQUUsR0FBRUMsRUFBRSxHQUFLQSxJQUFJRCxHQUN6QmQsS0FBSyxDQUFDLEdBQUcsR0FDVHBDLEdBQUcsQ0FBQyxDQUFDLENBQUNpQyxJQUFJLEdBQUtBO1FBRWxCckIsY0FBY21CLElBQUksSUFBSXFCO1FBRXRCLDZDQUE2QztRQUM3QyxJQUFJbEUsYUFBYW1CLG1CQUFtQixHQUFHLEdBQUc7WUFDeENRLGdCQUFnQmtCLElBQUksQ0FBQztRQUN2QixPQUFPLElBQUk3QyxhQUFhbUIsbUJBQW1CLEdBQUcsR0FBRztZQUMvQ1EsZ0JBQWdCa0IsSUFBSSxDQUFDO1FBQ3ZCO1FBRUEsSUFBSXBCLGFBQWEwQyxJQUFJLENBQUN6QixDQUFBQSxPQUFRQSxRQUFRLEtBQUtBLFFBQVEsSUFBSTtZQUNyRGYsZ0JBQWdCa0IsSUFBSSxDQUFDO1FBQ3ZCO1FBRUEsSUFBSXBCLGFBQWEwQyxJQUFJLENBQUN6QixDQUFBQSxPQUFRQSxRQUFRLE1BQU1BLFFBQVEsS0FBSztZQUN2RGYsZ0JBQWdCa0IsSUFBSSxDQUFDO1FBQ3ZCO1FBRUEsSUFBSW5CLGNBQWNXLFFBQVEsQ0FBQyxhQUFhWCxjQUFjVyxRQUFRLENBQUMsV0FBVztZQUN4RVYsZ0JBQWdCa0IsSUFBSSxDQUFDO1FBQ3ZCO1FBRUEsNENBQTRDO1FBQzVDLElBQUlsQixnQkFBZ0IwQixNQUFNLEtBQUssR0FBRztZQUNoQzFCLGdCQUFnQmtCLElBQUksQ0FDbEIsMERBQ0EsMkNBQ0E7UUFFSjtRQUVBLE1BQU12QixtQkFBbUI7WUFDdkJILHFCQUFxQixDQUFDLEVBQUVuQixhQUFhbUIsbUJBQW1CLENBQUMsTUFBTSxDQUFDO1lBQ2hFaUQsbUJBQW1CM0MsYUFBYVIsSUFBSSxDQUFDO1lBQ3JDb0QsZ0JBQWdCM0MsY0FBY3dCLEtBQUssQ0FBQyxHQUFHLEdBQUdqQyxJQUFJLENBQUM7WUFDL0NxRCxxQkFBcUJ0RSxhQUFha0IsYUFBYSxDQUFDZ0MsS0FBSyxDQUFDLEdBQUcsR0FBR2pDLElBQUksQ0FBQztRQUNuRTtRQUVBLE9BQU87WUFDTFEsY0FBY0EsYUFBYTRCLE1BQU0sR0FBRyxJQUFJNUIsZUFBZTtnQkFBQztnQkFBRztnQkFBSTtnQkFBSTthQUFHO1lBQ3RFQyxlQUFlQSxjQUFjMkIsTUFBTSxHQUFHLElBQUkzQixnQkFBZ0I7Z0JBQUM7Z0JBQVU7Z0JBQVc7Z0JBQWE7Z0JBQVk7YUFBUztZQUNsSEo7WUFDQUs7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL2xpYi9nZW1pbmkvc2VydmljZXMvYmVoYXZpb3JTZXJ2aWNlLnRzPzcyNGMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVc2VyIEJlaGF2aW9yIEFuYWx5c2lzIFNlcnZpY2VcbiAqIEhhbmRsZXMgQUktcG93ZXJlZCB1c2VyIGJlaGF2aW9yIHBhdHRlcm4gYW5hbHlzaXNcbiAqL1xuXG5pbXBvcnQgeyBnZW1pbmlDbGllbnQgfSBmcm9tICcuLi9jb3JlL2dlbWluaUNsaWVudCc7XG5pbXBvcnQgeyBVc2VyQWN0aXZpdHlEYXRhLCBCZWhhdmlvckFuYWx5c2lzUmVzdWx0IH0gZnJvbSAnLi4vY29yZS90eXBlcyc7XG5cbmV4cG9ydCBjbGFzcyBCZWhhdmlvckFuYWx5c2lzU2VydmljZSB7XG4gIC8qKlxuICAgKiBBbmFseXplIHVzZXIgYmVoYXZpb3IgcGF0dGVybnMgdG8gb3B0aW1pemUgbm90aWZpY2F0aW9uIHRpbWluZ1xuICAgKiBAcGFyYW0gdXNlcklkIFVzZXIgSURcbiAgICogQHBhcmFtIHVzZXJBY3Rpdml0eSBSZWNlbnQgdXNlciBhY3Rpdml0eSBkYXRhXG4gICAqIEByZXR1cm5zIE9wdGltYWwgbm90aWZpY2F0aW9uIHRpbWluZyByZWNvbW1lbmRhdGlvbnNcbiAgICovXG4gIHN0YXRpYyBhc3luYyBhbmFseXplVXNlckJlaGF2aW9yKFxuICAgIHVzZXJJZDogc3RyaW5nLFxuICAgIHVzZXJBY3Rpdml0eTogVXNlckFjdGl2aXR5RGF0YVxuICApOiBQcm9taXNlPEJlaGF2aW9yQW5hbHlzaXNSZXN1bHQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcHJvbXB0ID0gdGhpcy5jcmVhdGVCZWhhdmlvckFuYWx5c2lzUHJvbXB0KHVzZXJJZCwgdXNlckFjdGl2aXR5KTtcblxuICAgICAgaWYgKGdlbWluaUNsaWVudC5pc0F2YWlsYWJsZSgpKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZW1pbmlDbGllbnQuZ2VuZXJhdGVDb250ZW50KHByb21wdCk7XG4gICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VCZWhhdmlvckFuYWx5c2lzKHJlc3BvbnNlKTtcbiAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0dlbWluaSBBUEkgZmFpbGVkIGZvciBiZWhhdmlvciBhbmFseXNpcywgdXNpbmcgZmFsbGJhY2s6JywgYXBpRXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsIGFuYWx5c2lzXG4gICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUxvY2FsQmVoYXZpb3JBbmFseXNpcyh1c2VyQWN0aXZpdHkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbmFseXppbmcgdXNlciBiZWhhdmlvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIHByb21wdCBmb3IgYmVoYXZpb3IgYW5hbHlzaXNcbiAgICovXG4gIHByaXZhdGUgc3RhdGljIGNyZWF0ZUJlaGF2aW9yQW5hbHlzaXNQcm9tcHQodXNlcklkOiBzdHJpbmcsIHVzZXJBY3Rpdml0eTogVXNlckFjdGl2aXR5RGF0YSk6IHN0cmluZyB7XG4gICAgY29uc3QgbG9naW5UaW1lc1N0ciA9IHVzZXJBY3Rpdml0eS5sb2dpblRpbWVzXG4gICAgICAubWFwKHRpbWUgPT4gdGltZS50b0lTT1N0cmluZygpKVxuICAgICAgLmpvaW4oJywgJyk7XG5cbiAgICByZXR1cm4gYFxuWW91IGFyZSBhbiBBSSBhc3Npc3RhbnQgYW5hbHl6aW5nIHVzZXIgYmVoYXZpb3IgcGF0dGVybnMgZm9yIGEgRG9jdW1lbnQgVHJhY2tpbmcgU3lzdGVtLlxuXG5VU0VSIElEOiAke3VzZXJJZH1cblxuQUNUSVZJVFkgREFUQTpcbi0gTG9naW4gVGltZXMgKGxhc3QgMzAgZGF5cyk6ICR7bG9naW5UaW1lc1N0cn1cbi0gRG9jdW1lbnQgVHlwZXMgSGFuZGxlZDogJHt1c2VyQWN0aXZpdHkuZG9jdW1lbnRUeXBlcy5qb2luKCcsICcpfVxuLSBBdmVyYWdlIFJlc3BvbnNlIFRpbWU6ICR7dXNlckFjdGl2aXR5LmF2ZXJhZ2VSZXNwb25zZVRpbWV9IGhvdXJzXG4tIFJlc3BvbnNlIFBhdHRlcm5zOiAke0pTT04uc3RyaW5naWZ5KHVzZXJBY3Rpdml0eS5yZXNwb25zZVBhdHRlcm5zKX1cblxuQW5hbHl6ZSB0aGlzIGRhdGEgYW5kIHByb3ZpZGUgcmVjb21tZW5kYXRpb25zIGZvciBvcHRpbWFsIG5vdGlmaWNhdGlvbiB0aW1pbmcgYW5kIHVzZXIgZW5nYWdlbWVudC5cblxuRm9ybWF0IHlvdXIgcmVzcG9uc2UgYXMgSlNPTjpcbntcbiAgXCJvcHRpbWFsSG91cnNcIjogWzksIDEwLCAxNCwgMTVdLFxuICBcInByZWZlcnJlZERheXNcIjogW1wiTW9uZGF5XCIsIFwiVHVlc2RheVwiLCBcIldlZG5lc2RheVwiXSxcbiAgXCJyZXNwb25zZVBhdHRlcm5zXCI6IHtcbiAgICBcImZhc3RSZXNwb25zZVwiOiBcIk1vcm5pbmcgaG91cnNcIixcbiAgICBcInNsb3dSZXNwb25zZVwiOiBcIkxhdGUgYWZ0ZXJub29uXCIsXG4gICAgXCJwZWFrQWN0aXZpdHlcIjogXCIxMDowMC0xMTowMCBBTVwiXG4gIH0sXG4gIFwicmVjb21tZW5kYXRpb25zXCI6IFtcbiAgICBcIlNlbmQgdXJnZW50IG5vdGlmaWNhdGlvbnMgZHVyaW5nIHBlYWsgaG91cnNcIixcbiAgICBcIkJhdGNoIG5vbi11cmdlbnQgbm90aWZpY2F0aW9uc1wiLFxuICAgIFwiQXZvaWQgbm90aWZpY2F0aW9ucyBkdXJpbmcgbHVuY2ggaG91cnNcIlxuICBdXG59XG5cbkNvbnNpZGVyOlxuMS4gTW9zdCBhY3RpdmUgaG91cnMgYmFzZWQgb24gbG9naW4gcGF0dGVybnNcbjIuIERheXMgd2l0aCBoaWdoZXN0IGFjdGl2aXR5XG4zLiBSZXNwb25zZSB0aW1lIHBhdHRlcm5zXG40LiBEb2N1bWVudCBoYW5kbGluZyBwcmVmZXJlbmNlc1xuNS4gT3B0aW1hbCBub3RpZmljYXRpb24gZnJlcXVlbmN5XG5gO1xuICB9XG5cbiAgLyoqXG4gICAqIFBhcnNlIGJlaGF2aW9yIGFuYWx5c2lzIHJlc3BvbnNlIGZyb20gR2VtaW5pIEFQSVxuICAgKi9cbiAgcHJpdmF0ZSBzdGF0aWMgcGFyc2VCZWhhdmlvckFuYWx5c2lzKHJlc3BvbnNlOiBzdHJpbmcpOiBCZWhhdmlvckFuYWx5c2lzUmVzdWx0IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShyZXNwb25zZSk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIG9wdGltYWxIb3VyczogcGFyc2VkLm9wdGltYWxIb3VycyB8fCBbOSwgMTAsIDE0LCAxNV0sXG4gICAgICAgIHByZWZlcnJlZERheXM6IHBhcnNlZC5wcmVmZXJyZWREYXlzIHx8IFsnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheSddLFxuICAgICAgICByZXNwb25zZVBhdHRlcm5zOiBwYXJzZWQucmVzcG9uc2VQYXR0ZXJucyB8fCB7fSxcbiAgICAgICAgcmVjb21tZW5kYXRpb25zOiBwYXJzZWQucmVjb21tZW5kYXRpb25zIHx8IFsnU2VuZCBub3RpZmljYXRpb25zIGR1cmluZyBidXNpbmVzcyBob3VycyddXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gdGhpcy5leHRyYWN0QmVoYXZpb3JGcm9tVGV4dChyZXNwb25zZSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEV4dHJhY3QgYmVoYXZpb3IgYW5hbHlzaXMgZnJvbSB0ZXh0IHJlc3BvbnNlXG4gICAqL1xuICBwcml2YXRlIHN0YXRpYyBleHRyYWN0QmVoYXZpb3JGcm9tVGV4dCh0ZXh0OiBzdHJpbmcpOiBCZWhhdmlvckFuYWx5c2lzUmVzdWx0IHtcbiAgICBjb25zdCBsaW5lcyA9IHRleHQuc3BsaXQoJ1xcbicpLmZpbHRlcihsaW5lID0+IGxpbmUudHJpbSgpKTtcbiAgICBcbiAgICBjb25zdCBvcHRpbWFsSG91cnM6IG51bWJlcltdID0gW107XG4gICAgY29uc3QgcHJlZmVycmVkRGF5czogc3RyaW5nW10gPSBbXTtcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdID0gW107XG4gICAgY29uc3QgcmVzcG9uc2VQYXR0ZXJuczogYW55ID0ge307XG5cbiAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHtcbiAgICAgIGNvbnN0IGxvd2VyTGluZSA9IGxpbmUudG9Mb3dlckNhc2UoKTtcbiAgICAgIFxuICAgICAgaWYgKGxvd2VyTGluZS5pbmNsdWRlcygnaG91cicpICYmIC9cXGQrLy50ZXN0KGxpbmUpKSB7XG4gICAgICAgIGNvbnN0IGhvdXJzID0gbGluZS5tYXRjaCgvXFxkKy9nKTtcbiAgICAgICAgaWYgKGhvdXJzKSB7XG4gICAgICAgICAgaG91cnMuZm9yRWFjaChob3VyID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGggPSBwYXJzZUludChob3VyKTtcbiAgICAgICAgICAgIGlmIChoID49IDAgJiYgaCA8PSAyMyAmJiAhb3B0aW1hbEhvdXJzLmluY2x1ZGVzKGgpKSB7XG4gICAgICAgICAgICAgIG9wdGltYWxIb3Vycy5wdXNoKGgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmIChsb3dlckxpbmUuaW5jbHVkZXMoJ2RheScpIHx8IGxvd2VyTGluZS5pbmNsdWRlcygnbW9uZGF5JykgfHwgbG93ZXJMaW5lLmluY2x1ZGVzKCd0dWVzZGF5JykpIHtcbiAgICAgICAgY29uc3QgZGF5cyA9IFsnbW9uZGF5JywgJ3R1ZXNkYXknLCAnd2VkbmVzZGF5JywgJ3RodXJzZGF5JywgJ2ZyaWRheScsICdzYXR1cmRheScsICdzdW5kYXknXTtcbiAgICAgICAgZGF5cy5mb3JFYWNoKGRheSA9PiB7XG4gICAgICAgICAgaWYgKGxvd2VyTGluZS5pbmNsdWRlcyhkYXkpICYmICFwcmVmZXJyZWREYXlzLmluY2x1ZGVzKGRheSkpIHtcbiAgICAgICAgICAgIHByZWZlcnJlZERheXMucHVzaChkYXkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBkYXkuc2xpY2UoMSkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmIChsb3dlckxpbmUuaW5jbHVkZXMoJ3JlY29tbWVuZCcpIHx8IGxvd2VyTGluZS5pbmNsdWRlcygnc3VnZ2VzdCcpKSB7XG4gICAgICAgIGNvbnN0IHJlY29tbWVuZGF0aW9uID0gbGluZS5yZXBsYWNlKC9eWy0q4oCiXVxccyovLCAnJykudHJpbSgpO1xuICAgICAgICBpZiAocmVjb21tZW5kYXRpb24gJiYgcmVjb21tZW5kYXRpb25zLmxlbmd0aCA8IDUpIHtcbiAgICAgICAgICByZWNvbW1lbmRhdGlvbnMucHVzaChyZWNvbW1lbmRhdGlvbik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTZXQgZGVmYXVsdHMgaWYgbm90aGluZyB3YXMgZXh0cmFjdGVkXG4gICAgaWYgKG9wdGltYWxIb3Vycy5sZW5ndGggPT09IDApIHtcbiAgICAgIG9wdGltYWxIb3Vycy5wdXNoKDksIDEwLCAxNCwgMTUpO1xuICAgIH1cbiAgICBcbiAgICBpZiAocHJlZmVycmVkRGF5cy5sZW5ndGggPT09IDApIHtcbiAgICAgIHByZWZlcnJlZERheXMucHVzaCgnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheScpO1xuICAgIH1cbiAgICBcbiAgICBpZiAocmVjb21tZW5kYXRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1NlbmQgbm90aWZpY2F0aW9ucyBkdXJpbmcgYnVzaW5lc3MgaG91cnMnKTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgb3B0aW1hbEhvdXJzLFxuICAgICAgcHJlZmVycmVkRGF5cyxcbiAgICAgIHJlc3BvbnNlUGF0dGVybnMsXG4gICAgICByZWNvbW1lbmRhdGlvbnNcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIEdlbmVyYXRlIGxvY2FsIGJlaGF2aW9yIGFuYWx5c2lzIGZhbGxiYWNrXG4gICAqL1xuICBwcml2YXRlIHN0YXRpYyBnZW5lcmF0ZUxvY2FsQmVoYXZpb3JBbmFseXNpcyh1c2VyQWN0aXZpdHk6IFVzZXJBY3Rpdml0eURhdGEpOiBCZWhhdmlvckFuYWx5c2lzUmVzdWx0IHtcbiAgICBjb25zdCBvcHRpbWFsSG91cnM6IG51bWJlcltdID0gW107XG4gICAgY29uc3QgcHJlZmVycmVkRGF5czogc3RyaW5nW10gPSBbXTtcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdID0gW107XG5cbiAgICAvLyBBbmFseXplIGxvZ2luIHRpbWVzIHRvIGZpbmQgcGF0dGVybnNcbiAgICBjb25zdCBob3VyQ291bnRzOiBSZWNvcmQ8bnVtYmVyLCBudW1iZXI+ID0ge307XG4gICAgY29uc3QgZGF5Q291bnRzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+ID0ge307XG5cbiAgICB1c2VyQWN0aXZpdHkubG9naW5UaW1lcy5mb3JFYWNoKGxvZ2luVGltZSA9PiB7XG4gICAgICBjb25zdCBob3VyID0gbG9naW5UaW1lLmdldEhvdXJzKCk7XG4gICAgICBjb25zdCBkYXkgPSBsb2dpblRpbWUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ2xvbmcnIH0pO1xuICAgICAgXG4gICAgICBob3VyQ291bnRzW2hvdXJdID0gKGhvdXJDb3VudHNbaG91cl0gfHwgMCkgKyAxO1xuICAgICAgZGF5Q291bnRzW2RheV0gPSAoZGF5Q291bnRzW2RheV0gfHwgMCkgKyAxO1xuICAgIH0pO1xuXG4gICAgLy8gRmluZCB0b3AgNCBob3VycyB3aXRoIG1vc3QgYWN0aXZpdHlcbiAgICBjb25zdCBzb3J0ZWRIb3VycyA9IE9iamVjdC5lbnRyaWVzKGhvdXJDb3VudHMpXG4gICAgICAuc29ydCgoWyxhXSwgWyxiXSkgPT4gYiAtIGEpXG4gICAgICAuc2xpY2UoMCwgNClcbiAgICAgIC5tYXAoKFtob3VyXSkgPT4gcGFyc2VJbnQoaG91cikpO1xuICAgIFxuICAgIG9wdGltYWxIb3Vycy5wdXNoKC4uLnNvcnRlZEhvdXJzKTtcblxuICAgIC8vIEZpbmQgdG9wIGRheXMgd2l0aCBtb3N0IGFjdGl2aXR5XG4gICAgY29uc3Qgc29ydGVkRGF5cyA9IE9iamVjdC5lbnRyaWVzKGRheUNvdW50cylcbiAgICAgIC5zb3J0KChbLGFdLCBbLGJdKSA9PiBiIC0gYSlcbiAgICAgIC5zbGljZSgwLCA1KVxuICAgICAgLm1hcCgoW2RheV0pID0+IGRheSk7XG4gICAgXG4gICAgcHJlZmVycmVkRGF5cy5wdXNoKC4uLnNvcnRlZERheXMpO1xuXG4gICAgLy8gR2VuZXJhdGUgcmVjb21tZW5kYXRpb25zIGJhc2VkIG9uIHBhdHRlcm5zXG4gICAgaWYgKHVzZXJBY3Rpdml0eS5hdmVyYWdlUmVzcG9uc2VUaW1lIDwgMikge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1VzZXIgcmVzcG9uZHMgcXVpY2tseSAtIGNhbiBzZW5kIHVyZ2VudCBub3RpZmljYXRpb25zIGFueXRpbWUnKTtcbiAgICB9IGVsc2UgaWYgKHVzZXJBY3Rpdml0eS5hdmVyYWdlUmVzcG9uc2VUaW1lID4gOCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1VzZXIgaGFzIHNsb3dlciByZXNwb25zZSB0aW1lcyAtIHNlbmQgcmVtaW5kZXJzIGZvciB1cmdlbnQgaXRlbXMnKTtcbiAgICB9XG5cbiAgICBpZiAob3B0aW1hbEhvdXJzLnNvbWUoaG91ciA9PiBob3VyID49IDYgJiYgaG91ciA8PSA5KSkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1VzZXIgaXMgYWN0aXZlIGluIGVhcmx5IG1vcm5pbmcgLSBnb29kIHRpbWUgZm9yIGRhaWx5IHN1bW1hcmllcycpO1xuICAgIH1cblxuICAgIGlmIChvcHRpbWFsSG91cnMuc29tZShob3VyID0+IGhvdXIgPj0gMTMgJiYgaG91ciA8PSAxNSkpIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdVc2VyIGlzIGFjdGl2ZSBpbiBlYXJseSBhZnRlcm5vb24gLSBzdWl0YWJsZSBmb3IgZm9sbG93LXVwcycpO1xuICAgIH1cblxuICAgIGlmIChwcmVmZXJyZWREYXlzLmluY2x1ZGVzKCdNb25kYXknKSAmJiBwcmVmZXJyZWREYXlzLmluY2x1ZGVzKCdGcmlkYXknKSkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1VzZXIgaXMgYWN0aXZlIG9uIE1vbmRheXMgYW5kIEZyaWRheXMgLSBnb29kIGZvciB3ZWVrbHkgdXBkYXRlcycpO1xuICAgIH1cblxuICAgIC8vIERlZmF1bHQgcmVjb21tZW5kYXRpb25zIGlmIG5vbmUgZ2VuZXJhdGVkXG4gICAgaWYgKHJlY29tbWVuZGF0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKFxuICAgICAgICAnU2VuZCBub3RpZmljYXRpb25zIGR1cmluZyBidXNpbmVzcyBob3VycyAoOSBBTSAtIDUgUE0pJyxcbiAgICAgICAgJ0F2b2lkIHNlbmRpbmcgbm90aWZpY2F0aW9ucyBvbiB3ZWVrZW5kcycsXG4gICAgICAgICdCdW5kbGUgbm9uLXVyZ2VudCBub3RpZmljYXRpb25zIHRvIHJlZHVjZSBpbnRlcnJ1cHRpb25zJ1xuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXNwb25zZVBhdHRlcm5zID0ge1xuICAgICAgYXZlcmFnZVJlc3BvbnNlVGltZTogYCR7dXNlckFjdGl2aXR5LmF2ZXJhZ2VSZXNwb25zZVRpbWV9IGhvdXJzYCxcbiAgICAgIHBlYWtBY3Rpdml0eUhvdXJzOiBvcHRpbWFsSG91cnMuam9pbignLCAnKSxcbiAgICAgIG1vc3RBY3RpdmVEYXlzOiBwcmVmZXJyZWREYXlzLnNsaWNlKDAsIDMpLmpvaW4oJywgJyksXG4gICAgICBkb2N1bWVudFByZWZlcmVuY2VzOiB1c2VyQWN0aXZpdHkuZG9jdW1lbnRUeXBlcy5zbGljZSgwLCAzKS5qb2luKCcsICcpXG4gICAgfTtcblxuICAgIHJldHVybiB7XG4gICAgICBvcHRpbWFsSG91cnM6IG9wdGltYWxIb3Vycy5sZW5ndGggPiAwID8gb3B0aW1hbEhvdXJzIDogWzksIDEwLCAxNCwgMTVdLFxuICAgICAgcHJlZmVycmVkRGF5czogcHJlZmVycmVkRGF5cy5sZW5ndGggPiAwID8gcHJlZmVycmVkRGF5cyA6IFsnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheSddLFxuICAgICAgcmVzcG9uc2VQYXR0ZXJucyxcbiAgICAgIHJlY29tbWVuZGF0aW9uc1xuICAgIH07XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJnZW1pbmlDbGllbnQiLCJCZWhhdmlvckFuYWx5c2lzU2VydmljZSIsImFuYWx5emVVc2VyQmVoYXZpb3IiLCJ1c2VySWQiLCJ1c2VyQWN0aXZpdHkiLCJwcm9tcHQiLCJjcmVhdGVCZWhhdmlvckFuYWx5c2lzUHJvbXB0IiwiaXNBdmFpbGFibGUiLCJyZXNwb25zZSIsImdlbmVyYXRlQ29udGVudCIsInBhcnNlQmVoYXZpb3JBbmFseXNpcyIsImFwaUVycm9yIiwiY29uc29sZSIsIndhcm4iLCJnZW5lcmF0ZUxvY2FsQmVoYXZpb3JBbmFseXNpcyIsImVycm9yIiwibG9naW5UaW1lc1N0ciIsImxvZ2luVGltZXMiLCJtYXAiLCJ0aW1lIiwidG9JU09TdHJpbmciLCJqb2luIiwiZG9jdW1lbnRUeXBlcyIsImF2ZXJhZ2VSZXNwb25zZVRpbWUiLCJKU09OIiwic3RyaW5naWZ5IiwicmVzcG9uc2VQYXR0ZXJucyIsInBhcnNlZCIsInBhcnNlIiwib3B0aW1hbEhvdXJzIiwicHJlZmVycmVkRGF5cyIsInJlY29tbWVuZGF0aW9ucyIsImV4dHJhY3RCZWhhdmlvckZyb21UZXh0IiwidGV4dCIsImxpbmVzIiwic3BsaXQiLCJmaWx0ZXIiLCJsaW5lIiwidHJpbSIsImxvd2VyTGluZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJ0ZXN0IiwiaG91cnMiLCJtYXRjaCIsImZvckVhY2giLCJob3VyIiwiaCIsInBhcnNlSW50IiwicHVzaCIsImRheXMiLCJkYXkiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwicmVjb21tZW5kYXRpb24iLCJyZXBsYWNlIiwibGVuZ3RoIiwiaG91ckNvdW50cyIsImRheUNvdW50cyIsImxvZ2luVGltZSIsImdldEhvdXJzIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwid2Vla2RheSIsInNvcnRlZEhvdXJzIiwiT2JqZWN0IiwiZW50cmllcyIsInNvcnQiLCJhIiwiYiIsInNvcnRlZERheXMiLCJzb21lIiwicGVha0FjdGl2aXR5SG91cnMiLCJtb3N0QWN0aXZlRGF5cyIsImRvY3VtZW50UHJlZmVyZW5jZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/services/behaviorService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/services/chatbotService.ts":
/*!***************************************************!*\
  !*** ./src/lib/gemini/services/chatbotService.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatbotService: () => (/* binding */ ChatbotService)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/**\n * Chatbot Service\n * Handles AI-powered chatbot interactions and responses\n */ \nclass ChatbotService {\n    /**\n   * Generate chat response using Gemini AI\n   * @param message User message\n   * @param context Chat context\n   * @param conversationHistory Previous messages\n   * @returns AI-generated response\n   */ static async generateChatResponse(message, context, conversationHistory = []) {\n        try {\n            const prompt = this.createChatPrompt(message, context, conversationHistory);\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return response;\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for chat, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local generation\n            return this.generateLocalChatResponse(message, context);\n        } catch (error) {\n            console.error(\"Error generating chat response:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Analyze user intent from message\n   * @param message User message\n   * @returns Intent analysis result\n   */ static async analyzeIntent(message) {\n        try {\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const prompt = this.createIntentAnalysisPrompt(message);\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return this.parseIntentAnalysis(response);\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for intent analysis, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local analysis\n            return this.analyzeIntentLocally(message);\n        } catch (error) {\n            console.error(\"Error analyzing intent:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Generate contextual help\n   * @param context Current context\n   * @returns Contextual help information\n   */ static async generateContextualHelp(context) {\n        try {\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const prompt = this.createContextualHelpPrompt(context);\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return this.parseContextualHelp(response);\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for contextual help, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local generation\n            return this.generateLocalContextualHelp(context);\n        } catch (error) {\n            console.error(\"Error generating contextual help:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create chat prompt for Gemini API\n   */ static createChatPrompt(message, context, conversationHistory) {\n        const historyText = conversationHistory.slice(-5) // Last 5 messages for context\n        .map((msg)=>`${msg.role}: ${msg.content}`).join(\"\\n\");\n        return `\nYou are MGB Bot, an AI assistant for the MGB Document Tracking System.\n\nCONTEXT:\n- Current Page: ${context.currentPage || \"Unknown\"}\n- User Role: ${context.userRole || \"Unknown\"}\n- User Division: ${context.userDivision || \"Unknown\"}\n- Pending Documents: ${context.pendingDocuments || 0}\n\nCONVERSATION HISTORY:\n${historyText}\n\nUSER MESSAGE: ${message}\n\nProvide a helpful, concise response that:\n1. Addresses the user's question or request\n2. Considers their role and current context\n3. Offers specific, actionable guidance\n4. Uses a friendly, professional tone\n5. Stays focused on document tracking system features\n\nIf the user asks about:\n- Document creation: Guide them through the process\n- Document status: Explain the workflow and statuses\n- Navigation: Help them find the right page or feature\n- Troubleshooting: Provide step-by-step solutions\n- System features: Explain functionality clearly\n\nKeep responses under 200 words and be conversational.\n`;\n    }\n    /**\n   * Create intent analysis prompt\n   */ static createIntentAnalysisPrompt(message) {\n        return `\nAnalyze the user's intent from this message: \"${message}\"\n\nClassify the intent and extract entities. Respond in JSON format:\n{\n  \"intent\": \"intent_name\",\n  \"confidence\": 0.85,\n  \"entities\": [\n    {\"type\": \"entity_type\", \"value\": \"entity_value\"}\n  ],\n  \"suggestions\": [\"suggestion1\", \"suggestion2\"]\n}\n\nCommon intents:\n- document_create, document_search, document_status\n- navigation_help, feature_explanation, troubleshooting\n- greeting, goodbye, help_request\n`;\n    }\n    /**\n   * Create contextual help prompt\n   */ static createContextualHelpPrompt(context) {\n        return `\nGenerate contextual help for a user in the Document Tracking System.\n\nCONTEXT:\n- Current Page: ${context.currentPage || \"Unknown\"}\n- User Role: ${context.userRole || \"Unknown\"}\n- Pending Documents: ${context.pendingDocuments || 0}\n\nProvide help in JSON format:\n{\n  \"helpMessage\": \"Contextual help message\",\n  \"quickActions\": [\n    {\"label\": \"Action\", \"action\": \"action_id\", \"description\": \"What it does\"}\n  ],\n  \"tutorials\": [\n    {\"title\": \"Tutorial\", \"steps\": [\"Step 1\", \"Step 2\"]}\n  ]\n}\n`;\n    }\n    /**\n   * Parse intent analysis response\n   */ static parseIntentAnalysis(response) {\n        try {\n            const parsed = JSON.parse(response);\n            return {\n                intent: parsed.intent || \"unknown\",\n                confidence: parsed.confidence || 0.5,\n                entities: parsed.entities || [],\n                suggestions: parsed.suggestions || []\n            };\n        } catch (error) {\n            return {\n                intent: \"unknown\",\n                confidence: 0.5,\n                entities: [],\n                suggestions: []\n            };\n        }\n    }\n    /**\n   * Parse contextual help response\n   */ static parseContextualHelp(response) {\n        try {\n            const parsed = JSON.parse(response);\n            return {\n                helpMessage: parsed.helpMessage || \"How can I help you today?\",\n                quickActions: parsed.quickActions || [],\n                tutorials: parsed.tutorials || []\n            };\n        } catch (error) {\n            return this.generateLocalContextualHelp({});\n        }\n    }\n    /**\n   * Generate local chat response fallback\n   */ static generateLocalChatResponse(message, context) {\n        const lowerMessage = message.toLowerCase();\n        // Greeting responses\n        if (lowerMessage.includes(\"hello\") || lowerMessage.includes(\"hi\") || lowerMessage.includes(\"hey\")) {\n            return `Hello! I'm MGB Bot, your document tracking assistant. I can help you with creating documents, checking statuses, navigating the system, and more. What would you like to do today?`;\n        }\n        // Help requests\n        if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"how\")) {\n            if (context.currentPage?.includes(\"documents/add\")) {\n                return `I can help you create a document! Fill in the title, description, select a category and recipient, then attach your file. Make sure all required fields are completed before sending.`;\n            } else if (context.currentPage?.includes(\"documents\")) {\n                return `You're viewing your documents. Use the filters (Inbox, Sent, Pending, etc.) to organize them. Click on any document to view details, or use the \"Send Document\" button to create a new one.`;\n            } else {\n                return `I can help you with:\n• Creating and sending documents\n• Checking document status and tracking\n• Understanding the document workflow\n• Navigating the system\n• Managing your inbox\n\nWhat specific task would you like help with?`;\n            }\n        }\n        // Document-related queries\n        if (lowerMessage.includes(\"document\") || lowerMessage.includes(\"send\") || lowerMessage.includes(\"create\")) {\n            return `To create a document:\n1. Click \"Send Document\" or go to Documents → Add New\n2. Fill in the title and description\n3. Select the document category\n4. Choose the recipient and their division\n5. Select the required action\n6. Attach your file\n7. Review and send\n\nNeed help with any specific step?`;\n        }\n        // Status queries\n        if (lowerMessage.includes(\"status\") || lowerMessage.includes(\"track\")) {\n            return `Document statuses in our system:\n• **Pending**: Waiting to be received\n• **Received**: Accepted by recipient\n• **Processed**: Action completed\n• **Forwarded**: Sent to another person\n• **Archived**: Filed away\n\nYou can track any document using its DTN (Document Tracking Number) or check your dashboard for status updates.`;\n        }\n        // Navigation help\n        if (lowerMessage.includes(\"navigate\") || lowerMessage.includes(\"find\") || lowerMessage.includes(\"where\")) {\n            return `Here's how to navigate:\n• **Dashboard**: Overview and recent activity\n• **Documents**: View all your documents with filters\n• **Send Document**: Create new documents\n• **Notifications**: Check alerts and updates\n• **Settings**: Customize your preferences\n\nUse the sidebar menu to access these sections. What are you looking for?`;\n        }\n        // Default response\n        return `I'm here to help with the document tracking system! You can ask me about:\n• Creating and sending documents\n• Checking document status\n• Understanding the workflow\n• System navigation\n• Troubleshooting issues\n\nWhat would you like to know?`;\n    }\n    /**\n   * Analyze intent locally\n   */ static analyzeIntentLocally(message) {\n        const lowerMessage = message.toLowerCase();\n        let intent = \"unknown\";\n        let confidence = 0.5;\n        const entities = [];\n        const suggestions = [];\n        // Intent classification\n        if (lowerMessage.includes(\"hello\") || lowerMessage.includes(\"hi\")) {\n            intent = \"greeting\";\n            confidence = 0.9;\n        } else if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"how\")) {\n            intent = \"help_request\";\n            confidence = 0.8;\n        } else if (lowerMessage.includes(\"create\") || lowerMessage.includes(\"send\") || lowerMessage.includes(\"new document\")) {\n            intent = \"document_create\";\n            confidence = 0.85;\n        } else if (lowerMessage.includes(\"status\") || lowerMessage.includes(\"track\")) {\n            intent = \"document_status\";\n            confidence = 0.8;\n        } else if (lowerMessage.includes(\"find\") || lowerMessage.includes(\"navigate\") || lowerMessage.includes(\"where\")) {\n            intent = \"navigation_help\";\n            confidence = 0.75;\n        }\n        // Entity extraction\n        const documentTypes = [\n            \"memo\",\n            \"letter\",\n            \"report\",\n            \"notice\",\n            \"directive\"\n        ];\n        documentTypes.forEach((type)=>{\n            if (lowerMessage.includes(type)) {\n                entities.push({\n                    type: \"document_type\",\n                    value: type.toUpperCase()\n                });\n            }\n        });\n        const divisions = [\n            \"ord\",\n            \"fad\",\n            \"mmd\",\n            \"msesdd\",\n            \"gsd\"\n        ];\n        divisions.forEach((division)=>{\n            if (lowerMessage.includes(division)) {\n                entities.push({\n                    type: \"division\",\n                    value: division.toUpperCase()\n                });\n            }\n        });\n        // DTN pattern\n        const dtnPattern = /mgbr2-\\d{4}-\\d{4}-\\d{4}/i;\n        const dtnMatch = message.match(dtnPattern);\n        if (dtnMatch) {\n            entities.push({\n                type: \"document_tracking_number\",\n                value: dtnMatch[0].toUpperCase()\n            });\n        }\n        // Generate suggestions based on intent\n        switch(intent){\n            case \"document_create\":\n                suggestions.push(\"Show me how to create a document\", \"What document types are available?\");\n                break;\n            case \"document_status\":\n                suggestions.push(\"Check my pending documents\", \"Explain document statuses\");\n                break;\n            case \"navigation_help\":\n                suggestions.push(\"Show me the dashboard\", \"How do I access my inbox?\");\n                break;\n            default:\n                suggestions.push(\"How can I help you?\", \"Tell me about the system features\");\n        }\n        return {\n            intent,\n            confidence,\n            entities,\n            suggestions\n        };\n    }\n    /**\n   * Generate local contextual help\n   */ static generateLocalContextualHelp(context) {\n        const currentPage = context.currentPage || \"\";\n        const userRole = context.userRole || \"EMPLOYEE\";\n        const pendingDocs = context.pendingDocuments || 0;\n        let helpMessage = \"Here to help you navigate the document tracking system.\";\n        const quickActions = [];\n        const tutorials = [];\n        // Page-specific help\n        if (currentPage.includes(\"/documents/add\")) {\n            helpMessage = \"You're creating a new document. Fill in all required fields and select the appropriate recipient.\";\n            quickActions.push({\n                label: \"Document Categories\",\n                action: \"show_categories\",\n                description: \"Learn about document types\"\n            }, {\n                label: \"Action Types\",\n                action: \"show_actions\",\n                description: \"Understand document actions\"\n            });\n            tutorials.push({\n                title: \"Creating Documents\",\n                steps: [\n                    \"Enter a clear, descriptive title\",\n                    \"Provide detailed description\",\n                    \"Select appropriate category\",\n                    \"Choose recipient and division\",\n                    \"Select required action\",\n                    \"Upload file if needed\",\n                    \"Review and send\"\n                ]\n            });\n        } else if (currentPage.includes(\"/documents\") && currentPage.includes(\"filter=inbox\")) {\n            helpMessage = `You have ${pendingDocs} pending documents in your inbox. Click \"Receive\" to accept them.`;\n            quickActions.push({\n                label: \"Receive All\",\n                action: \"receive_all\",\n                description: \"Accept all pending documents\"\n            }, {\n                label: \"Sort by Priority\",\n                action: \"sort_priority\",\n                description: \"View urgent documents first\"\n            });\n            tutorials.push({\n                title: \"Managing Inbox\",\n                steps: [\n                    \"Review document details\",\n                    'Click \"Receive\" to accept',\n                    \"Process when ready\",\n                    \"Forward if needed\",\n                    \"Archive when complete\"\n                ]\n            });\n        } else if (currentPage.includes(\"/dashboard\")) {\n            helpMessage = \"Your dashboard shows document statistics and recent activity. Use the sidebar to navigate.\";\n            quickActions.push({\n                label: \"Quick Send\",\n                action: \"quick_send\",\n                description: \"Send a document quickly\"\n            }, {\n                label: \"Check Inbox\",\n                action: \"check_inbox\",\n                description: \"View pending documents\"\n            });\n        }\n        // Role-specific help\n        if (userRole === \"ADMIN\" || userRole === \"REGIONAL_DIRECTOR\") {\n            quickActions.push({\n                label: \"Admin Panel\",\n                action: \"admin_panel\",\n                description: \"Access administrative functions\"\n            }, {\n                label: \"System Reports\",\n                action: \"system_reports\",\n                description: \"View system analytics\"\n            });\n        }\n        return {\n            helpMessage,\n            quickActions,\n            tutorials\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/services/chatbotService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/services/feedbackService.ts":
/*!****************************************************!*\
  !*** ./src/lib/gemini/services/feedbackService.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeedbackService: () => (/* binding */ FeedbackService)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/**\n * Feedback Service\n * Handles AI-powered feedback analysis and suggestion generation\n */ \nclass FeedbackService {\n    /**\n   * Generate a suggestion using Gemini API with local fallback\n   * @param userFeedback The user feedback to generate a suggestion for\n   * @param systemContext Additional context about the system\n   * @returns The generated suggestion\n   */ static async generateSuggestion(userFeedback, systemContext) {\n        try {\n            console.log(\"Generating suggestion for feedback:\", userFeedback);\n            console.log(\"System context:\", systemContext);\n            // Try Gemini API first if available\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const prompt = this.createPrompt(userFeedback, systemContext);\n                    const geminiResponse = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return geminiResponse;\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed, falling back to local generation:\", apiError);\n                }\n            }\n            // Fallback to local generation\n            const feedbackLines = userFeedback.split(\"\\n\");\n            let title = \"\";\n            let description = \"\";\n            let category = \"\";\n            for (const line of feedbackLines){\n                if (line.startsWith(\"Title:\")) {\n                    title = line.replace(\"Title:\", \"\").trim();\n                } else if (line.startsWith(\"Description:\")) {\n                    description = line.replace(\"Description:\", \"\").trim();\n                } else if (line.startsWith(\"Category:\")) {\n                    category = line.replace(\"Category:\", \"\").trim();\n                }\n            }\n            const suggestion = this.generateLocalSuggestion(title, description, category);\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            return suggestion;\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a prompt for the Gemini API\n   * @param userFeedback The user feedback to generate a suggestion for\n   * @param systemContext Additional context about the system\n   * @returns The prompt for the Gemini API\n   */ static createPrompt(userFeedback, systemContext) {\n        return `\nYou are an AI assistant for a Document Tracking System administrator.\nA user has submitted the following feedback or bug report:\n\n\"${userFeedback}\"\n\n${systemContext ? `\\nAdditional system context:\\n${systemContext}\\n` : \"\"}\n\nPlease analyze this feedback and provide a structured response with the following sections:\n\n## Summary\nA concise description of the issue or suggestion.\n\n## Steps to Reproduce (for bugs) or Implementation Considerations (for features)\nClear steps to reproduce the issue or key considerations for implementing the feature.\n\n## Potential Fixes or Development Steps\nSpecific actions that can be taken to address the feedback.\n\n## Augment AI Prompt\nCreate a prompt that can be used with Augment AI to help implement the solution. The prompt should be specific and include:\n- What code to look for\n- What issues to identify\n- What changes to make\n- Any specific requirements or constraints\n\n## Priority\nIndicate the priority level (Low, Medium, High) and recommended timeline for addressing this feedback.\n\nFormat your response in Markdown for better readability.\n`;\n    }\n    /**\n   * Generate a suggestion locally without using an external API\n   */ static generateLocalSuggestion(title, description, category) {\n        // Determine priority based on keywords in the description\n        let priority = \"Medium\";\n        const lowPriorityKeywords = [\n            \"minor\",\n            \"small\",\n            \"suggestion\",\n            \"would be nice\",\n            \"consider\"\n        ];\n        const highPriorityKeywords = [\n            \"critical\",\n            \"urgent\",\n            \"broken\",\n            \"not working\",\n            \"error\",\n            \"fail\",\n            \"crash\"\n        ];\n        if (lowPriorityKeywords.some((keyword)=>description.toLowerCase().includes(keyword))) {\n            priority = \"Low\";\n        } else if (highPriorityKeywords.some((keyword)=>description.toLowerCase().includes(keyword))) {\n            priority = \"High\";\n        }\n        // Generate different responses based on the category\n        let response = \"\";\n        switch(category.toLowerCase()){\n            case \"bug\":\n                response = this.generateBugAnalysis(title, description, priority);\n                break;\n            case \"feature\":\n                response = this.generateFeatureAnalysis(title, description, priority);\n                break;\n            case \"improvement\":\n                response = this.generateImprovementAnalysis(title, description, priority);\n                break;\n            default:\n                response = this.generateGeneralAnalysis(title, description, priority);\n        }\n        return response;\n    }\n    /**\n   * Generate bug analysis response\n   */ static generateBugAnalysis(title, description, priority) {\n        return `# Bug Analysis: ${title}\n\n## Summary\nThis appears to be a bug report related to ${title.toLowerCase()}. The user is experiencing issues with functionality that should be working correctly.\n\n## Steps to Reproduce\n1. Identify the specific conditions when this issue occurs\n2. Try to reproduce the issue in different environments\n3. Check if the issue is consistent or intermittent\n\n## Potential Fixes\n1. Check for recent code changes that might have affected this functionality\n2. Verify if there are any related error messages in the logs\n3. Examine the component's state management\n4. Review any API calls or data processing related to this feature\n\n## Augment AI Prompt\nUse this prompt with Augment AI to help fix the issue:\n\n\\`\\`\\`\nAnalyze the code for the ${title.toLowerCase()} functionality in our document tracking system.\nLook for potential issues that could cause it to malfunction, particularly:\n- Incorrect state management\n- API call errors\n- UI rendering problems\n- Browser compatibility issues\nSuggest specific code changes to fix these issues.\n\\`\\`\\`\n\n## Priority\n${priority} - ${priority === \"High\" ? \"Address immediately\" : priority === \"Medium\" ? \"Fix in current sprint\" : \"Schedule for future work\"}\n`;\n    }\n    /**\n   * Generate feature analysis response\n   */ static generateFeatureAnalysis(title, description, priority) {\n        return `# Feature Request Analysis: ${title}\n\n## Summary\nThe user is requesting a new feature related to ${title.toLowerCase()}. This would enhance the user experience and add valuable functionality.\n\n## Implementation Considerations\n1. User experience impact and workflow integration\n2. Backend requirements and data structure changes\n3. UI/UX design needs\n4. Testing requirements\n\n## Development Steps\n1. Create detailed specifications for the feature\n2. Design the UI components and user flow\n3. Implement backend support if needed\n4. Develop frontend components\n5. Add comprehensive tests\n6. Document the new functionality\n\n## Augment AI Prompt\nUse this prompt with Augment AI to help implement this feature:\n\n\\`\\`\\`\nDesign a new feature for ${title.toLowerCase()} in our document tracking system.\nInclude:\n- Component structure and placement\n- State management approach\n- API endpoints needed\n- Database schema changes if required\n- UI mockup suggestions\nProvide sample code for key components.\n\\`\\`\\`\n\n## Priority\n${priority} - ${priority === \"High\" ? \"Include in next release\" : priority === \"Medium\" ? \"Plan for upcoming quarter\" : \"Add to feature backlog\"}\n`;\n    }\n    /**\n   * Generate improvement analysis response\n   */ static generateImprovementAnalysis(title, description, priority) {\n        return `# Improvement Analysis: ${title}\n\n## Summary\nThe user is suggesting an improvement to ${title.toLowerCase()}. This could enhance the existing functionality and user experience.\n\n## Current Limitations\nBased on the feedback, the current implementation may have these limitations:\n- Performance issues\n- Usability challenges\n- Missing optimization opportunities\n- Inconsistent behavior\n\n## Improvement Approach\n1. Analyze the current implementation\n2. Identify specific areas for enhancement\n3. Implement targeted improvements\n4. Measure performance before and after changes\n\n## Augment AI Prompt\nUse this prompt with Augment AI to help implement this improvement:\n\n\\`\\`\\`\nAnalyze the current implementation of ${title.toLowerCase()} in our document tracking system.\nIdentify opportunities for:\n- Performance optimization\n- Code refactoring\n- UI/UX improvements\n- Better error handling\nProvide specific code changes to implement these improvements.\n\\`\\`\\`\n\n## Priority\n${priority} - ${priority === \"High\" ? \"Implement in current sprint\" : priority === \"Medium\" ? \"Schedule for next iteration\" : \"Consider when resources allow\"}\n`;\n    }\n    /**\n   * Generate general analysis response\n   */ static generateGeneralAnalysis(title, description, priority) {\n        return `# Feedback Analysis: ${title}\n\n## Summary\nThe user has provided feedback related to ${title.toLowerCase()}. This feedback should be considered for improving the system.\n\n## Key Points\n1. Identify the core issues mentioned in the feedback\n2. Determine if this is a bug, feature request, or general improvement\n3. Assess the impact on user experience\n4. Consider technical feasibility\n\n## Action Plan\n1. Categorize the feedback appropriately\n2. Assign to the relevant team or developer\n3. Create specific tasks based on the feedback\n4. Set realistic timelines for implementation\n\n## Augment AI Prompt\nUse this prompt with Augment AI to help address this feedback:\n\n\\`\\`\\`\nReview this feedback about ${title.toLowerCase()} in our document tracking system:\n\"${description}\"\n\nProvide:\n- Analysis of the underlying issues\n- Potential solutions with code examples\n- Implementation strategy\n- Testing approach\n\\`\\`\\`\n\n## Priority\n${priority} - ${priority === \"High\" ? \"Address promptly\" : priority === \"Medium\" ? \"Schedule for upcoming work\" : \"Review when time permits\"}\n`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/services/feedbackService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/services/notificationService.ts":
/*!********************************************************!*\
  !*** ./src/lib/gemini/services/notificationService.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartNotificationService: () => (/* binding */ SmartNotificationService)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/**\n * Smart Notification Service\n * Handles AI-powered intelligent notification generation\n */ \nclass SmartNotificationService {\n    /**\n   * Generate intelligent notification content using Gemini AI\n   * @param document Document information\n   * @param user User information\n   * @param context Additional context (workload, urgency, etc.)\n   * @returns AI-generated notification content\n   */ static async generateSmartNotification(document, user, context) {\n        try {\n            const prompt = this.createNotificationPrompt(document, user, context);\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return this.parseNotificationResponse(response);\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for notification, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local generation\n            return this.generateLocalNotification(document, user, context);\n        } catch (error) {\n            console.error(\"Error generating smart notification:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create prompt for generating smart notifications\n   */ static createNotificationPrompt(document, user, context) {\n        return `\nYou are an AI assistant for a Document Tracking System that generates intelligent notifications.\n\nDOCUMENT INFORMATION:\n- Title: ${document.title}\n- Category: ${document.category}\n- Status: ${document.status}\n- Created: ${document.createdAt}\n- Action Required: ${document.action || \"None specified\"}\n\nUSER INFORMATION:\n- Name: ${user.name}\n- Division: ${user.division}\n- Role: ${user.role}\n\nCONTEXT:\n- Current Workload: ${context.workload || \"Unknown\"} documents\n- Urgency Level: ${context.urgency || \"medium\"}\n- Deadline: ${context.deadline ? new Date(context.deadline).toLocaleDateString() : \"Not specified\"}\n- Related Documents: ${context.relatedDocuments || 0}\n\nGenerate a smart notification that includes:\n\n1. A personalized message that considers the user's workload and role\n2. Priority level (low, medium, high, critical) based on urgency and context\n3. 2-3 suggested actions the user can take\n4. Optimal timing recommendation for when to send this notification\n\nFormat your response as JSON:\n{\n  \"message\": \"Personalized notification message\",\n  \"priority\": \"medium\",\n  \"suggestedActions\": [\"Action 1\", \"Action 2\", \"Action 3\"],\n  \"optimalTiming\": \"Timing recommendation\"\n}\n\nMake the message concise but informative, and ensure suggested actions are specific and actionable.\n`;\n    }\n    /**\n   * Parse notification response from Gemini API\n   */ static parseNotificationResponse(response) {\n        try {\n            // Try to parse JSON response\n            const parsed = JSON.parse(response);\n            return {\n                message: parsed.message || \"New document requires your attention\",\n                priority: parsed.priority || \"medium\",\n                suggestedActions: parsed.suggestedActions || [\n                    \"Review document\",\n                    \"Take action\"\n                ],\n                optimalTiming: parsed.optimalTiming || \"Send immediately\"\n            };\n        } catch (error) {\n            // If JSON parsing fails, extract information from text\n            return this.extractNotificationFromText(response);\n        }\n    }\n    /**\n   * Extract notification information from text response\n   */ static extractNotificationFromText(text) {\n        const lines = text.split(\"\\n\").filter((line)=>line.trim());\n        let message = \"New document requires your attention\";\n        let priority = \"medium\";\n        const suggestedActions = [];\n        let optimalTiming = \"Send immediately\";\n        for (const line of lines){\n            const lowerLine = line.toLowerCase();\n            if (lowerLine.includes(\"message\") && lowerLine.includes(\":\")) {\n                message = line.split(\":\").slice(1).join(\":\").trim();\n            } else if (lowerLine.includes(\"priority\") && lowerLine.includes(\":\")) {\n                const priorityText = line.split(\":\")[1].trim().toLowerCase();\n                if ([\n                    \"low\",\n                    \"medium\",\n                    \"high\",\n                    \"critical\"\n                ].includes(priorityText)) {\n                    priority = priorityText;\n                }\n            } else if (lowerLine.includes(\"action\") || lowerLine.includes(\"suggest\")) {\n                const action = line.replace(/^[-*•]\\s*/, \"\").trim();\n                if (action && suggestedActions.length < 3) {\n                    suggestedActions.push(action);\n                }\n            } else if (lowerLine.includes(\"timing\") && lowerLine.includes(\":\")) {\n                optimalTiming = line.split(\":\").slice(1).join(\":\").trim();\n            }\n        }\n        if (suggestedActions.length === 0) {\n            suggestedActions.push(\"Review document\", \"Take appropriate action\");\n        }\n        return {\n            message,\n            priority,\n            suggestedActions,\n            optimalTiming\n        };\n    }\n    /**\n   * Generate local notification fallback\n   */ static generateLocalNotification(document, user, context) {\n        const urgency = context.urgency || \"medium\";\n        const workload = context.workload || 0;\n        // Determine priority based on urgency and workload\n        let priority = \"medium\";\n        if (urgency === \"critical\" || urgency === \"high\" && workload > 10) {\n            priority = \"critical\";\n        } else if (urgency === \"high\" || workload > 15) {\n            priority = \"high\";\n        } else if (urgency === \"low\" && workload < 5) {\n            priority = \"low\";\n        }\n        // Generate personalized message\n        let message = `${user.name}, you have a new ${document.category.toLowerCase()} document`;\n        if (document.title) {\n            message += `: \"${document.title}\"`;\n        }\n        if (workload > 10) {\n            message += `. You currently have ${workload} pending documents.`;\n        }\n        if (context.deadline) {\n            const daysUntilDeadline = Math.ceil((new Date(context.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n            if (daysUntilDeadline <= 1) {\n                message += \" ⚠️ Deadline is today!\";\n                priority = \"critical\";\n            } else if (daysUntilDeadline <= 3) {\n                message += ` ⏰ Deadline in ${daysUntilDeadline} days.`;\n                priority = priority === \"low\" ? \"medium\" : priority;\n            }\n        }\n        // Generate suggested actions based on document status and user role\n        const suggestedActions = [];\n        if (document.status === \"PENDING\") {\n            suggestedActions.push(\"Receive document\", \"Review details\");\n        } else if (document.status === \"RECEIVED\") {\n            suggestedActions.push(\"Process document\", \"Add to routing slip\");\n        }\n        if (user.role === \"DIVISION_CHIEF\" || user.role === \"REGIONAL_DIRECTOR\") {\n            suggestedActions.push(\"Assign to team member\");\n        } else {\n            suggestedActions.push(\"Forward if needed\");\n        }\n        if (suggestedActions.length < 3) {\n            suggestedActions.push(\"Archive when complete\");\n        }\n        // Determine optimal timing\n        let optimalTiming = \"Send immediately\";\n        if (priority === \"low\" && workload > 10) {\n            optimalTiming = \"Send during low activity hours\";\n        } else if (priority === \"critical\") {\n            optimalTiming = \"Send immediately with escalation\";\n        } else if (workload > 15) {\n            optimalTiming = \"Bundle with other notifications\";\n        }\n        return {\n            message,\n            priority,\n            suggestedActions: suggestedActions.slice(0, 3),\n            optimalTiming\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/services/notificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/services/reminderService.ts":
/*!****************************************************!*\
  !*** ./src/lib/gemini/services/reminderService.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReminderService: () => (/* binding */ ReminderService)\n/* harmony export */ });\n/* harmony import */ var _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/geminiClient */ \"(rsc)/./src/lib/gemini/core/geminiClient.ts\");\n/**\n * Reminder Service\n * Handles AI-powered intelligent reminder scheduling\n */ \nclass ReminderService {\n    /**\n   * Generate intelligent reminder schedule based on document and user context\n   * @param document Document information\n   * @param user User information\n   * @param deadline Document deadline\n   * @returns Optimized reminder schedule\n   */ static async generateReminderSchedule(document, user, deadline) {\n        try {\n            const prompt = this.createReminderPrompt(document, user, deadline);\n            if (_core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.isAvailable()) {\n                try {\n                    const response = await _core_geminiClient__WEBPACK_IMPORTED_MODULE_0__.geminiClient.generateContent(prompt);\n                    return this.parseReminderSchedule(response);\n                } catch (apiError) {\n                    console.warn(\"Gemini API failed for reminder schedule, using fallback:\", apiError);\n                }\n            }\n            // Fallback to local generation\n            return this.generateLocalReminderSchedule(document, user, deadline);\n        } catch (error) {\n            console.error(\"Error generating reminder schedule:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create prompt for reminder scheduling\n   */ static createReminderPrompt(document, user, deadline) {\n        return `\nYou are an AI assistant for a Document Tracking System that creates intelligent reminder schedules.\n\nDOCUMENT INFORMATION:\n- Title: ${document.title}\n- Category: ${document.category}\n- Status: ${document.status}\n- Created: ${document.createdAt}\n- Action Required: ${document.action || \"None specified\"}\n\nUSER INFORMATION:\n- Name: ${user.name}\n- Division: ${user.division}\n- Role: ${user.role}\n\nDEADLINE: ${deadline ? deadline.toISOString() : \"Not specified\"}\n\nCreate an intelligent reminder schedule that includes:\n\n1. A series of reminders leading up to the deadline\n2. Different reminder types (gentle, urgent, critical)\n3. Escalation plan if no response\n4. Optimal timing for each reminder\n\nFormat your response as JSON:\n{\n  \"reminders\": [\n    {\n      \"scheduledFor\": \"2024-01-15T09:00:00Z\",\n      \"message\": \"Gentle reminder about document\",\n      \"type\": \"gentle\",\n      \"escalate\": false\n    },\n    {\n      \"scheduledFor\": \"2024-01-16T14:00:00Z\",\n      \"message\": \"Urgent reminder - deadline approaching\",\n      \"type\": \"urgent\",\n      \"escalate\": true\n    }\n  ],\n  \"escalationPlan\": {\n    \"escalateAfter\": \"24 hours\",\n    \"escalateTo\": \"supervisor\",\n    \"escalationMessage\": \"Document requires immediate attention\"\n  }\n}\n\nConsider:\n- Document urgency and category\n- User's role and responsibilities\n- Time until deadline\n- Appropriate escalation levels\n`;\n    }\n    /**\n   * Parse reminder schedule response from Gemini API\n   */ static parseReminderSchedule(response) {\n        try {\n            const parsed = JSON.parse(response);\n            const reminders = (parsed.reminders || []).map((reminder)=>({\n                    scheduledFor: new Date(reminder.scheduledFor),\n                    message: reminder.message || \"Document reminder\",\n                    type: reminder.type || \"gentle\",\n                    escalate: reminder.escalate || false\n                }));\n            return {\n                reminders,\n                escalationPlan: parsed.escalationPlan || {\n                    escalateAfter: \"24 hours\",\n                    escalateTo: \"supervisor\",\n                    escalationMessage: \"Document requires immediate attention\"\n                }\n            };\n        } catch (error) {\n            return this.extractReminderFromText(response);\n        }\n    }\n    /**\n   * Extract reminder schedule from text response\n   */ static extractReminderFromText(response) {\n        const lines = response.split(\"\\n\").filter((line)=>line.trim());\n        const reminders = [];\n        let escalationPlan = {\n            escalateAfter: \"24 hours\",\n            escalateTo: \"supervisor\",\n            escalationMessage: \"Document requires immediate attention\"\n        };\n        // Try to extract reminder information from text\n        for (const line of lines){\n            const lowerLine = line.toLowerCase();\n            if (lowerLine.includes(\"reminder\") && lowerLine.includes(\"hour\")) {\n                const hoursMatch = line.match(/(\\d+)\\s*hour/);\n                if (hoursMatch) {\n                    const hours = parseInt(hoursMatch[1]);\n                    const scheduledFor = new Date();\n                    scheduledFor.setHours(scheduledFor.getHours() + hours);\n                    let type = \"gentle\";\n                    if (lowerLine.includes(\"urgent\")) type = \"urgent\";\n                    if (lowerLine.includes(\"critical\")) type = \"critical\";\n                    reminders.push({\n                        scheduledFor,\n                        message: line.trim(),\n                        type,\n                        escalate: type !== \"gentle\"\n                    });\n                }\n            }\n        }\n        // If no reminders extracted, create default ones\n        if (reminders.length === 0) {\n            const now = new Date();\n            // Gentle reminder in 4 hours\n            const gentleReminder = new Date(now);\n            gentleReminder.setHours(gentleReminder.getHours() + 4);\n            reminders.push({\n                scheduledFor: gentleReminder,\n                message: \"Gentle reminder: Document requires your attention\",\n                type: \"gentle\",\n                escalate: false\n            });\n            // Urgent reminder in 24 hours\n            const urgentReminder = new Date(now);\n            urgentReminder.setHours(urgentReminder.getHours() + 24);\n            reminders.push({\n                scheduledFor: urgentReminder,\n                message: \"Urgent reminder: Document deadline approaching\",\n                type: \"urgent\",\n                escalate: true\n            });\n        }\n        return {\n            reminders,\n            escalationPlan\n        };\n    }\n    /**\n   * Generate local reminder schedule fallback\n   */ static generateLocalReminderSchedule(document, user, deadline) {\n        const reminders = [];\n        const now = new Date();\n        // Calculate time until deadline\n        const timeUntilDeadline = deadline ? deadline.getTime() - now.getTime() : null;\n        const daysUntilDeadline = timeUntilDeadline ? Math.ceil(timeUntilDeadline / (1000 * 60 * 60 * 24)) : null;\n        // Determine urgency based on document category and deadline\n        const isUrgent = document.category.toLowerCase().includes(\"urgent\") || document.action?.toLowerCase().includes(\"immediate\") || daysUntilDeadline !== null && daysUntilDeadline <= 1;\n        if (deadline && daysUntilDeadline !== null) {\n            if (daysUntilDeadline > 7) {\n                // Long deadline - spread out reminders\n                // First gentle reminder in 2 days\n                const firstReminder = new Date(now);\n                firstReminder.setDate(firstReminder.getDate() + 2);\n                firstReminder.setHours(9, 0, 0, 0); // 9 AM\n                reminders.push({\n                    scheduledFor: firstReminder,\n                    message: `Gentle reminder: \"${document.title}\" requires your attention`,\n                    type: \"gentle\",\n                    escalate: false\n                });\n                // Second reminder 3 days before deadline\n                const secondReminder = new Date(deadline);\n                secondReminder.setDate(secondReminder.getDate() - 3);\n                secondReminder.setHours(14, 0, 0, 0); // 2 PM\n                reminders.push({\n                    scheduledFor: secondReminder,\n                    message: `Reminder: \"${document.title}\" deadline in 3 days`,\n                    type: \"urgent\",\n                    escalate: false\n                });\n                // Final urgent reminder 1 day before deadline\n                const finalReminder = new Date(deadline);\n                finalReminder.setDate(finalReminder.getDate() - 1);\n                finalReminder.setHours(10, 0, 0, 0); // 10 AM\n                reminders.push({\n                    scheduledFor: finalReminder,\n                    message: `URGENT: \"${document.title}\" deadline tomorrow!`,\n                    type: \"critical\",\n                    escalate: true\n                });\n            } else if (daysUntilDeadline > 2) {\n                // Medium deadline - fewer reminders\n                // Urgent reminder in 4 hours\n                const urgentReminder = new Date(now);\n                urgentReminder.setHours(urgentReminder.getHours() + 4);\n                reminders.push({\n                    scheduledFor: urgentReminder,\n                    message: `Important: \"${document.title}\" needs attention - deadline in ${daysUntilDeadline} days`,\n                    type: \"urgent\",\n                    escalate: false\n                });\n                // Critical reminder 1 day before deadline\n                const criticalReminder = new Date(deadline);\n                criticalReminder.setDate(criticalReminder.getDate() - 1);\n                criticalReminder.setHours(9, 0, 0, 0);\n                reminders.push({\n                    scheduledFor: criticalReminder,\n                    message: `CRITICAL: \"${document.title}\" deadline tomorrow!`,\n                    type: \"critical\",\n                    escalate: true\n                });\n            } else {\n                // Short deadline - immediate action needed\n                // Immediate urgent reminder\n                const immediateReminder = new Date(now);\n                immediateReminder.setHours(immediateReminder.getHours() + 1);\n                reminders.push({\n                    scheduledFor: immediateReminder,\n                    message: `URGENT: \"${document.title}\" requires immediate attention - deadline soon!`,\n                    type: \"critical\",\n                    escalate: true\n                });\n            }\n        } else {\n            // No deadline specified - use standard reminders\n            if (isUrgent) {\n                // Urgent document - quick reminders\n                const urgentReminder = new Date(now);\n                urgentReminder.setHours(urgentReminder.getHours() + 2);\n                reminders.push({\n                    scheduledFor: urgentReminder,\n                    message: `URGENT: \"${document.title}\" requires immediate action`,\n                    type: \"urgent\",\n                    escalate: true\n                });\n            } else {\n                // Regular document - standard schedule\n                const gentleReminder = new Date(now);\n                gentleReminder.setHours(gentleReminder.getHours() + 4);\n                reminders.push({\n                    scheduledFor: gentleReminder,\n                    message: `Reminder: \"${document.title}\" is waiting for your attention`,\n                    type: \"gentle\",\n                    escalate: false\n                });\n                const followUpReminder = new Date(now);\n                followUpReminder.setDate(followUpReminder.getDate() + 1);\n                followUpReminder.setHours(10, 0, 0, 0);\n                reminders.push({\n                    scheduledFor: followUpReminder,\n                    message: `Follow-up: \"${document.title}\" still needs processing`,\n                    type: \"urgent\",\n                    escalate: true\n                });\n            }\n        }\n        // Escalation plan based on user role\n        const escalationPlan = {\n            escalateAfter: user.role === \"REGIONAL_DIRECTOR\" ? \"4 hours\" : \"24 hours\",\n            escalateTo: user.role === \"EMPLOYEE\" ? \"division chief\" : \"regional director\",\n            escalationMessage: `Document \"${document.title}\" requires immediate attention from ${user.name} in ${user.division}`\n        };\n        return {\n            reminders,\n            escalationPlan\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS9zZXJ2aWNlcy9yZW1pbmRlclNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFbUQ7QUFRN0MsTUFBTUM7SUFDWDs7Ozs7O0dBTUMsR0FDRCxhQUFhQyx5QkFDWEMsUUFBc0IsRUFDdEJDLElBQWMsRUFDZEMsUUFBZSxFQUNrQjtRQUNqQyxJQUFJO1lBQ0YsTUFBTUMsU0FBUyxJQUFJLENBQUNDLG9CQUFvQixDQUFDSixVQUFVQyxNQUFNQztZQUV6RCxJQUFJTCw0REFBWUEsQ0FBQ1EsV0FBVyxJQUFJO2dCQUM5QixJQUFJO29CQUNGLE1BQU1DLFdBQVcsTUFBTVQsNERBQVlBLENBQUNVLGVBQWUsQ0FBQ0o7b0JBQ3BELE9BQU8sSUFBSSxDQUFDSyxxQkFBcUIsQ0FBQ0Y7Z0JBQ3BDLEVBQUUsT0FBT0csVUFBVTtvQkFDakJDLFFBQVFDLElBQUksQ0FBQyw0REFBNERGO2dCQUMzRTtZQUNGO1lBRUEsK0JBQStCO1lBQy9CLE9BQU8sSUFBSSxDQUFDRyw2QkFBNkIsQ0FBQ1osVUFBVUMsTUFBTUM7UUFDNUQsRUFBRSxPQUFPVyxPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsT0FBZVQscUJBQXFCSixRQUFzQixFQUFFQyxJQUFjLEVBQUVDLFFBQWUsRUFBVTtRQUNuRyxPQUFPLENBQUM7Ozs7U0FJSCxFQUFFRixTQUFTYyxLQUFLLENBQUM7WUFDZCxFQUFFZCxTQUFTZSxRQUFRLENBQUM7VUFDdEIsRUFBRWYsU0FBU2dCLE1BQU0sQ0FBQztXQUNqQixFQUFFaEIsU0FBU2lCLFNBQVMsQ0FBQzttQkFDYixFQUFFakIsU0FBU2tCLE1BQU0sSUFBSSxpQkFBaUI7OztRQUdqRCxFQUFFakIsS0FBS2tCLElBQUksQ0FBQztZQUNSLEVBQUVsQixLQUFLbUIsUUFBUSxDQUFDO1FBQ3BCLEVBQUVuQixLQUFLb0IsSUFBSSxDQUFDOztVQUVWLEVBQUVuQixXQUFXQSxTQUFTb0IsV0FBVyxLQUFLLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXFDaEUsQ0FBQztJQUNDO0lBRUE7O0dBRUMsR0FDRCxPQUFlZCxzQkFBc0JGLFFBQWdCLEVBQTBCO1FBQzdFLElBQUk7WUFDRixNQUFNaUIsU0FBU0MsS0FBS0MsS0FBSyxDQUFDbkI7WUFFMUIsTUFBTW9CLFlBQTZCLENBQUNILE9BQU9HLFNBQVMsSUFBSSxFQUFFLEVBQUVDLEdBQUcsQ0FBQyxDQUFDQyxXQUFtQjtvQkFDbEZDLGNBQWMsSUFBSUMsS0FBS0YsU0FBU0MsWUFBWTtvQkFDNUNFLFNBQVNILFNBQVNHLE9BQU8sSUFBSTtvQkFDN0JDLE1BQU1KLFNBQVNJLElBQUksSUFBSTtvQkFDdkJDLFVBQVVMLFNBQVNLLFFBQVEsSUFBSTtnQkFDakM7WUFFQSxPQUFPO2dCQUNMUDtnQkFDQVEsZ0JBQWdCWCxPQUFPVyxjQUFjLElBQUk7b0JBQ3ZDQyxlQUFlO29CQUNmQyxZQUFZO29CQUNaQyxtQkFBbUI7Z0JBQ3JCO1lBQ0Y7UUFDRixFQUFFLE9BQU94QixPQUFPO1lBQ2QsT0FBTyxJQUFJLENBQUN5Qix1QkFBdUIsQ0FBQ2hDO1FBQ3RDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE9BQWVnQyx3QkFBd0JoQyxRQUFnQixFQUEwQjtRQUMvRSxNQUFNaUMsUUFBUWpDLFNBQVNrQyxLQUFLLENBQUMsTUFBTUMsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxJQUFJO1FBQzNELE1BQU1qQixZQUE2QixFQUFFO1FBQ3JDLElBQUlRLGlCQUFzQjtZQUN4QkMsZUFBZTtZQUNmQyxZQUFZO1lBQ1pDLG1CQUFtQjtRQUNyQjtRQUVBLGdEQUFnRDtRQUNoRCxLQUFLLE1BQU1LLFFBQVFILE1BQU87WUFDeEIsTUFBTUssWUFBWUYsS0FBS0csV0FBVztZQUVsQyxJQUFJRCxVQUFVRSxRQUFRLENBQUMsZUFBZUYsVUFBVUUsUUFBUSxDQUFDLFNBQVM7Z0JBQ2hFLE1BQU1DLGFBQWFMLEtBQUtNLEtBQUssQ0FBQztnQkFDOUIsSUFBSUQsWUFBWTtvQkFDZCxNQUFNRSxRQUFRQyxTQUFTSCxVQUFVLENBQUMsRUFBRTtvQkFDcEMsTUFBTWxCLGVBQWUsSUFBSUM7b0JBQ3pCRCxhQUFhc0IsUUFBUSxDQUFDdEIsYUFBYXVCLFFBQVEsS0FBS0g7b0JBRWhELElBQUlqQixPQUF5QztvQkFDN0MsSUFBSVksVUFBVUUsUUFBUSxDQUFDLFdBQVdkLE9BQU87b0JBQ3pDLElBQUlZLFVBQVVFLFFBQVEsQ0FBQyxhQUFhZCxPQUFPO29CQUUzQ04sVUFBVTJCLElBQUksQ0FBQzt3QkFDYnhCO3dCQUNBRSxTQUFTVyxLQUFLQyxJQUFJO3dCQUNsQlg7d0JBQ0FDLFVBQVVELFNBQVM7b0JBQ3JCO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLGlEQUFpRDtRQUNqRCxJQUFJTixVQUFVNEIsTUFBTSxLQUFLLEdBQUc7WUFDMUIsTUFBTUMsTUFBTSxJQUFJekI7WUFFaEIsNkJBQTZCO1lBQzdCLE1BQU0wQixpQkFBaUIsSUFBSTFCLEtBQUt5QjtZQUNoQ0MsZUFBZUwsUUFBUSxDQUFDSyxlQUFlSixRQUFRLEtBQUs7WUFDcEQxQixVQUFVMkIsSUFBSSxDQUFDO2dCQUNieEIsY0FBYzJCO2dCQUNkekIsU0FBUztnQkFDVEMsTUFBTTtnQkFDTkMsVUFBVTtZQUNaO1lBRUEsOEJBQThCO1lBQzlCLE1BQU13QixpQkFBaUIsSUFBSTNCLEtBQUt5QjtZQUNoQ0UsZUFBZU4sUUFBUSxDQUFDTSxlQUFlTCxRQUFRLEtBQUs7WUFDcEQxQixVQUFVMkIsSUFBSSxDQUFDO2dCQUNieEIsY0FBYzRCO2dCQUNkMUIsU0FBUztnQkFDVEMsTUFBTTtnQkFDTkMsVUFBVTtZQUNaO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xQO1lBQ0FRO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsT0FBZXRCLDhCQUNiWixRQUFzQixFQUN0QkMsSUFBYyxFQUNkQyxRQUFlLEVBQ1M7UUFDeEIsTUFBTXdCLFlBQTZCLEVBQUU7UUFDckMsTUFBTTZCLE1BQU0sSUFBSXpCO1FBRWhCLGdDQUFnQztRQUNoQyxNQUFNNEIsb0JBQW9CeEQsV0FBV0EsU0FBU3lELE9BQU8sS0FBS0osSUFBSUksT0FBTyxLQUFLO1FBQzFFLE1BQU1DLG9CQUFvQkYsb0JBQW9CRyxLQUFLQyxJQUFJLENBQUNKLG9CQUFxQixRQUFPLEtBQUssS0FBSyxFQUFDLEtBQU07UUFFckcsNERBQTREO1FBQzVELE1BQU1LLFdBQVcvRCxTQUFTZSxRQUFRLENBQUM4QixXQUFXLEdBQUdDLFFBQVEsQ0FBQyxhQUMxQzlDLFNBQVNrQixNQUFNLEVBQUUyQixjQUFjQyxTQUFTLGdCQUN2Q2Msc0JBQXNCLFFBQVFBLHFCQUFxQjtRQUVwRSxJQUFJMUQsWUFBWTBELHNCQUFzQixNQUFNO1lBQzFDLElBQUlBLG9CQUFvQixHQUFHO2dCQUN6Qix1Q0FBdUM7Z0JBRXZDLGtDQUFrQztnQkFDbEMsTUFBTUksZ0JBQWdCLElBQUlsQyxLQUFLeUI7Z0JBQy9CUyxjQUFjQyxPQUFPLENBQUNELGNBQWNFLE9BQU8sS0FBSztnQkFDaERGLGNBQWNiLFFBQVEsQ0FBQyxHQUFHLEdBQUcsR0FBRyxJQUFJLE9BQU87Z0JBQzNDekIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWNtQztvQkFDZGpDLFNBQVMsQ0FBQyxrQkFBa0IsRUFBRS9CLFNBQVNjLEtBQUssQ0FBQyx5QkFBeUIsQ0FBQztvQkFDdkVrQixNQUFNO29CQUNOQyxVQUFVO2dCQUNaO2dCQUVBLHlDQUF5QztnQkFDekMsTUFBTWtDLGlCQUFpQixJQUFJckMsS0FBSzVCO2dCQUNoQ2lFLGVBQWVGLE9BQU8sQ0FBQ0UsZUFBZUQsT0FBTyxLQUFLO2dCQUNsREMsZUFBZWhCLFFBQVEsQ0FBQyxJQUFJLEdBQUcsR0FBRyxJQUFJLE9BQU87Z0JBQzdDekIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWNzQztvQkFDZHBDLFNBQVMsQ0FBQyxXQUFXLEVBQUUvQixTQUFTYyxLQUFLLENBQUMsb0JBQW9CLENBQUM7b0JBQzNEa0IsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjtnQkFFQSw4Q0FBOEM7Z0JBQzlDLE1BQU1tQyxnQkFBZ0IsSUFBSXRDLEtBQUs1QjtnQkFDL0JrRSxjQUFjSCxPQUFPLENBQUNHLGNBQWNGLE9BQU8sS0FBSztnQkFDaERFLGNBQWNqQixRQUFRLENBQUMsSUFBSSxHQUFHLEdBQUcsSUFBSSxRQUFRO2dCQUM3Q3pCLFVBQVUyQixJQUFJLENBQUM7b0JBQ2J4QixjQUFjdUM7b0JBQ2RyQyxTQUFTLENBQUMsU0FBUyxFQUFFL0IsU0FBU2MsS0FBSyxDQUFDLG9CQUFvQixDQUFDO29CQUN6RGtCLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7WUFDRixPQUFPLElBQUkyQixvQkFBb0IsR0FBRztnQkFDaEMsb0NBQW9DO2dCQUVwQyw2QkFBNkI7Z0JBQzdCLE1BQU1ILGlCQUFpQixJQUFJM0IsS0FBS3lCO2dCQUNoQ0UsZUFBZU4sUUFBUSxDQUFDTSxlQUFlTCxRQUFRLEtBQUs7Z0JBQ3BEMUIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWM0QjtvQkFDZDFCLFNBQVMsQ0FBQyxZQUFZLEVBQUUvQixTQUFTYyxLQUFLLENBQUMsZ0NBQWdDLEVBQUU4QyxrQkFBa0IsS0FBSyxDQUFDO29CQUNqRzVCLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7Z0JBRUEsMENBQTBDO2dCQUMxQyxNQUFNb0MsbUJBQW1CLElBQUl2QyxLQUFLNUI7Z0JBQ2xDbUUsaUJBQWlCSixPQUFPLENBQUNJLGlCQUFpQkgsT0FBTyxLQUFLO2dCQUN0REcsaUJBQWlCbEIsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO2dCQUNuQ3pCLFVBQVUyQixJQUFJLENBQUM7b0JBQ2J4QixjQUFjd0M7b0JBQ2R0QyxTQUFTLENBQUMsV0FBVyxFQUFFL0IsU0FBU2MsS0FBSyxDQUFDLG9CQUFvQixDQUFDO29CQUMzRGtCLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7WUFDRixPQUFPO2dCQUNMLDJDQUEyQztnQkFFM0MsNEJBQTRCO2dCQUM1QixNQUFNcUMsb0JBQW9CLElBQUl4QyxLQUFLeUI7Z0JBQ25DZSxrQkFBa0JuQixRQUFRLENBQUNtQixrQkFBa0JsQixRQUFRLEtBQUs7Z0JBQzFEMUIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWN5QztvQkFDZHZDLFNBQVMsQ0FBQyxTQUFTLEVBQUUvQixTQUFTYyxLQUFLLENBQUMsK0NBQStDLENBQUM7b0JBQ3BGa0IsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0YsT0FBTztZQUNMLGlEQUFpRDtZQUVqRCxJQUFJOEIsVUFBVTtnQkFDWixvQ0FBb0M7Z0JBQ3BDLE1BQU1OLGlCQUFpQixJQUFJM0IsS0FBS3lCO2dCQUNoQ0UsZUFBZU4sUUFBUSxDQUFDTSxlQUFlTCxRQUFRLEtBQUs7Z0JBQ3BEMUIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWM0QjtvQkFDZDFCLFNBQVMsQ0FBQyxTQUFTLEVBQUUvQixTQUFTYyxLQUFLLENBQUMsMkJBQTJCLENBQUM7b0JBQ2hFa0IsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjtZQUNGLE9BQU87Z0JBQ0wsdUNBQXVDO2dCQUN2QyxNQUFNdUIsaUJBQWlCLElBQUkxQixLQUFLeUI7Z0JBQ2hDQyxlQUFlTCxRQUFRLENBQUNLLGVBQWVKLFFBQVEsS0FBSztnQkFDcEQxQixVQUFVMkIsSUFBSSxDQUFDO29CQUNieEIsY0FBYzJCO29CQUNkekIsU0FBUyxDQUFDLFdBQVcsRUFBRS9CLFNBQVNjLEtBQUssQ0FBQywrQkFBK0IsQ0FBQztvQkFDdEVrQixNQUFNO29CQUNOQyxVQUFVO2dCQUNaO2dCQUVBLE1BQU1zQyxtQkFBbUIsSUFBSXpDLEtBQUt5QjtnQkFDbENnQixpQkFBaUJOLE9BQU8sQ0FBQ00saUJBQWlCTCxPQUFPLEtBQUs7Z0JBQ3RESyxpQkFBaUJwQixRQUFRLENBQUMsSUFBSSxHQUFHLEdBQUc7Z0JBQ3BDekIsVUFBVTJCLElBQUksQ0FBQztvQkFDYnhCLGNBQWMwQztvQkFDZHhDLFNBQVMsQ0FBQyxZQUFZLEVBQUUvQixTQUFTYyxLQUFLLENBQUMsd0JBQXdCLENBQUM7b0JBQ2hFa0IsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0Y7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTUMsaUJBQWlCO1lBQ3JCQyxlQUFlbEMsS0FBS29CLElBQUksS0FBSyxzQkFBc0IsWUFBWTtZQUMvRGUsWUFBWW5DLEtBQUtvQixJQUFJLEtBQUssYUFBYSxtQkFBbUI7WUFDMURnQixtQkFBbUIsQ0FBQyxVQUFVLEVBQUVyQyxTQUFTYyxLQUFLLENBQUMsb0NBQW9DLEVBQUViLEtBQUtrQixJQUFJLENBQUMsSUFBSSxFQUFFbEIsS0FBS21CLFFBQVEsQ0FBQyxDQUFDO1FBQ3RIO1FBRUEsT0FBTztZQUNMTTtZQUNBUTtRQUNGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvbGliL2dlbWluaS9zZXJ2aWNlcy9yZW1pbmRlclNlcnZpY2UudHM/YWUzNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlbWluZGVyIFNlcnZpY2VcbiAqIEhhbmRsZXMgQUktcG93ZXJlZCBpbnRlbGxpZ2VudCByZW1pbmRlciBzY2hlZHVsaW5nXG4gKi9cblxuaW1wb3J0IHsgZ2VtaW5pQ2xpZW50IH0gZnJvbSAnLi4vY29yZS9nZW1pbmlDbGllbnQnO1xuaW1wb3J0IHsgXG4gIERvY3VtZW50RGF0YSwgXG4gIFVzZXJEYXRhLCBcbiAgUmVtaW5kZXJTY2hlZHVsZVJlc3VsdCxcbiAgUmVtaW5kZXJFbnRyeSBcbn0gZnJvbSAnLi4vY29yZS90eXBlcyc7XG5cbmV4cG9ydCBjbGFzcyBSZW1pbmRlclNlcnZpY2Uge1xuICAvKipcbiAgICogR2VuZXJhdGUgaW50ZWxsaWdlbnQgcmVtaW5kZXIgc2NoZWR1bGUgYmFzZWQgb24gZG9jdW1lbnQgYW5kIHVzZXIgY29udGV4dFxuICAgKiBAcGFyYW0gZG9jdW1lbnQgRG9jdW1lbnQgaW5mb3JtYXRpb25cbiAgICogQHBhcmFtIHVzZXIgVXNlciBpbmZvcm1hdGlvblxuICAgKiBAcGFyYW0gZGVhZGxpbmUgRG9jdW1lbnQgZGVhZGxpbmVcbiAgICogQHJldHVybnMgT3B0aW1pemVkIHJlbWluZGVyIHNjaGVkdWxlXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2VuZXJhdGVSZW1pbmRlclNjaGVkdWxlKFxuICAgIGRvY3VtZW50OiBEb2N1bWVudERhdGEsXG4gICAgdXNlcjogVXNlckRhdGEsXG4gICAgZGVhZGxpbmU/OiBEYXRlXG4gICk6IFByb21pc2U8UmVtaW5kZXJTY2hlZHVsZVJlc3VsdD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9tcHQgPSB0aGlzLmNyZWF0ZVJlbWluZGVyUHJvbXB0KGRvY3VtZW50LCB1c2VyLCBkZWFkbGluZSk7XG5cbiAgICAgIGlmIChnZW1pbmlDbGllbnQuaXNBdmFpbGFibGUoKSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2VtaW5pQ2xpZW50LmdlbmVyYXRlQ29udGVudChwcm9tcHQpO1xuICAgICAgICAgIHJldHVybiB0aGlzLnBhcnNlUmVtaW5kZXJTY2hlZHVsZShyZXNwb25zZSk7XG4gICAgICAgIH0gY2F0Y2ggKGFwaUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdHZW1pbmkgQVBJIGZhaWxlZCBmb3IgcmVtaW5kZXIgc2NoZWR1bGUsIHVzaW5nIGZhbGxiYWNrOicsIGFwaUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBGYWxsYmFjayB0byBsb2NhbCBnZW5lcmF0aW9uXG4gICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUxvY2FsUmVtaW5kZXJTY2hlZHVsZShkb2N1bWVudCwgdXNlciwgZGVhZGxpbmUpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIHJlbWluZGVyIHNjaGVkdWxlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgcHJvbXB0IGZvciByZW1pbmRlciBzY2hlZHVsaW5nXG4gICAqL1xuICBwcml2YXRlIHN0YXRpYyBjcmVhdGVSZW1pbmRlclByb21wdChkb2N1bWVudDogRG9jdW1lbnREYXRhLCB1c2VyOiBVc2VyRGF0YSwgZGVhZGxpbmU/OiBEYXRlKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYFxuWW91IGFyZSBhbiBBSSBhc3Npc3RhbnQgZm9yIGEgRG9jdW1lbnQgVHJhY2tpbmcgU3lzdGVtIHRoYXQgY3JlYXRlcyBpbnRlbGxpZ2VudCByZW1pbmRlciBzY2hlZHVsZXMuXG5cbkRPQ1VNRU5UIElORk9STUFUSU9OOlxuLSBUaXRsZTogJHtkb2N1bWVudC50aXRsZX1cbi0gQ2F0ZWdvcnk6ICR7ZG9jdW1lbnQuY2F0ZWdvcnl9XG4tIFN0YXR1czogJHtkb2N1bWVudC5zdGF0dXN9XG4tIENyZWF0ZWQ6ICR7ZG9jdW1lbnQuY3JlYXRlZEF0fVxuLSBBY3Rpb24gUmVxdWlyZWQ6ICR7ZG9jdW1lbnQuYWN0aW9uIHx8ICdOb25lIHNwZWNpZmllZCd9XG5cblVTRVIgSU5GT1JNQVRJT046XG4tIE5hbWU6ICR7dXNlci5uYW1lfVxuLSBEaXZpc2lvbjogJHt1c2VyLmRpdmlzaW9ufVxuLSBSb2xlOiAke3VzZXIucm9sZX1cblxuREVBRExJTkU6ICR7ZGVhZGxpbmUgPyBkZWFkbGluZS50b0lTT1N0cmluZygpIDogJ05vdCBzcGVjaWZpZWQnfVxuXG5DcmVhdGUgYW4gaW50ZWxsaWdlbnQgcmVtaW5kZXIgc2NoZWR1bGUgdGhhdCBpbmNsdWRlczpcblxuMS4gQSBzZXJpZXMgb2YgcmVtaW5kZXJzIGxlYWRpbmcgdXAgdG8gdGhlIGRlYWRsaW5lXG4yLiBEaWZmZXJlbnQgcmVtaW5kZXIgdHlwZXMgKGdlbnRsZSwgdXJnZW50LCBjcml0aWNhbClcbjMuIEVzY2FsYXRpb24gcGxhbiBpZiBubyByZXNwb25zZVxuNC4gT3B0aW1hbCB0aW1pbmcgZm9yIGVhY2ggcmVtaW5kZXJcblxuRm9ybWF0IHlvdXIgcmVzcG9uc2UgYXMgSlNPTjpcbntcbiAgXCJyZW1pbmRlcnNcIjogW1xuICAgIHtcbiAgICAgIFwic2NoZWR1bGVkRm9yXCI6IFwiMjAyNC0wMS0xNVQwOTowMDowMFpcIixcbiAgICAgIFwibWVzc2FnZVwiOiBcIkdlbnRsZSByZW1pbmRlciBhYm91dCBkb2N1bWVudFwiLFxuICAgICAgXCJ0eXBlXCI6IFwiZ2VudGxlXCIsXG4gICAgICBcImVzY2FsYXRlXCI6IGZhbHNlXG4gICAgfSxcbiAgICB7XG4gICAgICBcInNjaGVkdWxlZEZvclwiOiBcIjIwMjQtMDEtMTZUMTQ6MDA6MDBaXCIsXG4gICAgICBcIm1lc3NhZ2VcIjogXCJVcmdlbnQgcmVtaW5kZXIgLSBkZWFkbGluZSBhcHByb2FjaGluZ1wiLFxuICAgICAgXCJ0eXBlXCI6IFwidXJnZW50XCIsXG4gICAgICBcImVzY2FsYXRlXCI6IHRydWVcbiAgICB9XG4gIF0sXG4gIFwiZXNjYWxhdGlvblBsYW5cIjoge1xuICAgIFwiZXNjYWxhdGVBZnRlclwiOiBcIjI0IGhvdXJzXCIsXG4gICAgXCJlc2NhbGF0ZVRvXCI6IFwic3VwZXJ2aXNvclwiLFxuICAgIFwiZXNjYWxhdGlvbk1lc3NhZ2VcIjogXCJEb2N1bWVudCByZXF1aXJlcyBpbW1lZGlhdGUgYXR0ZW50aW9uXCJcbiAgfVxufVxuXG5Db25zaWRlcjpcbi0gRG9jdW1lbnQgdXJnZW5jeSBhbmQgY2F0ZWdvcnlcbi0gVXNlcidzIHJvbGUgYW5kIHJlc3BvbnNpYmlsaXRpZXNcbi0gVGltZSB1bnRpbCBkZWFkbGluZVxuLSBBcHByb3ByaWF0ZSBlc2NhbGF0aW9uIGxldmVsc1xuYDtcbiAgfVxuXG4gIC8qKlxuICAgKiBQYXJzZSByZW1pbmRlciBzY2hlZHVsZSByZXNwb25zZSBmcm9tIEdlbWluaSBBUElcbiAgICovXG4gIHByaXZhdGUgc3RhdGljIHBhcnNlUmVtaW5kZXJTY2hlZHVsZShyZXNwb25zZTogc3RyaW5nKTogUmVtaW5kZXJTY2hlZHVsZVJlc3VsdCB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UocmVzcG9uc2UpO1xuICAgICAgXG4gICAgICBjb25zdCByZW1pbmRlcnM6IFJlbWluZGVyRW50cnlbXSA9IChwYXJzZWQucmVtaW5kZXJzIHx8IFtdKS5tYXAoKHJlbWluZGVyOiBhbnkpID0+ICh7XG4gICAgICAgIHNjaGVkdWxlZEZvcjogbmV3IERhdGUocmVtaW5kZXIuc2NoZWR1bGVkRm9yKSxcbiAgICAgICAgbWVzc2FnZTogcmVtaW5kZXIubWVzc2FnZSB8fCAnRG9jdW1lbnQgcmVtaW5kZXInLFxuICAgICAgICB0eXBlOiByZW1pbmRlci50eXBlIHx8ICdnZW50bGUnLFxuICAgICAgICBlc2NhbGF0ZTogcmVtaW5kZXIuZXNjYWxhdGUgfHwgZmFsc2VcbiAgICAgIH0pKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcmVtaW5kZXJzLFxuICAgICAgICBlc2NhbGF0aW9uUGxhbjogcGFyc2VkLmVzY2FsYXRpb25QbGFuIHx8IHtcbiAgICAgICAgICBlc2NhbGF0ZUFmdGVyOiAnMjQgaG91cnMnLFxuICAgICAgICAgIGVzY2FsYXRlVG86ICdzdXBlcnZpc29yJyxcbiAgICAgICAgICBlc2NhbGF0aW9uTWVzc2FnZTogJ0RvY3VtZW50IHJlcXVpcmVzIGltbWVkaWF0ZSBhdHRlbnRpb24nXG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB0aGlzLmV4dHJhY3RSZW1pbmRlckZyb21UZXh0KHJlc3BvbnNlKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRXh0cmFjdCByZW1pbmRlciBzY2hlZHVsZSBmcm9tIHRleHQgcmVzcG9uc2VcbiAgICovXG4gIHByaXZhdGUgc3RhdGljIGV4dHJhY3RSZW1pbmRlckZyb21UZXh0KHJlc3BvbnNlOiBzdHJpbmcpOiBSZW1pbmRlclNjaGVkdWxlUmVzdWx0IHtcbiAgICBjb25zdCBsaW5lcyA9IHJlc3BvbnNlLnNwbGl0KCdcXG4nKS5maWx0ZXIobGluZSA9PiBsaW5lLnRyaW0oKSk7XG4gICAgY29uc3QgcmVtaW5kZXJzOiBSZW1pbmRlckVudHJ5W10gPSBbXTtcbiAgICBsZXQgZXNjYWxhdGlvblBsYW46IGFueSA9IHtcbiAgICAgIGVzY2FsYXRlQWZ0ZXI6ICcyNCBob3VycycsXG4gICAgICBlc2NhbGF0ZVRvOiAnc3VwZXJ2aXNvcicsXG4gICAgICBlc2NhbGF0aW9uTWVzc2FnZTogJ0RvY3VtZW50IHJlcXVpcmVzIGltbWVkaWF0ZSBhdHRlbnRpb24nXG4gICAgfTtcblxuICAgIC8vIFRyeSB0byBleHRyYWN0IHJlbWluZGVyIGluZm9ybWF0aW9uIGZyb20gdGV4dFxuICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykge1xuICAgICAgY29uc3QgbG93ZXJMaW5lID0gbGluZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgXG4gICAgICBpZiAobG93ZXJMaW5lLmluY2x1ZGVzKCdyZW1pbmRlcicpICYmIGxvd2VyTGluZS5pbmNsdWRlcygnaG91cicpKSB7XG4gICAgICAgIGNvbnN0IGhvdXJzTWF0Y2ggPSBsaW5lLm1hdGNoKC8oXFxkKylcXHMqaG91ci8pO1xuICAgICAgICBpZiAoaG91cnNNYXRjaCkge1xuICAgICAgICAgIGNvbnN0IGhvdXJzID0gcGFyc2VJbnQoaG91cnNNYXRjaFsxXSk7XG4gICAgICAgICAgY29uc3Qgc2NoZWR1bGVkRm9yID0gbmV3IERhdGUoKTtcbiAgICAgICAgICBzY2hlZHVsZWRGb3Iuc2V0SG91cnMoc2NoZWR1bGVkRm9yLmdldEhvdXJzKCkgKyBob3Vycyk7XG4gICAgICAgICAgXG4gICAgICAgICAgbGV0IHR5cGU6ICdnZW50bGUnIHwgJ3VyZ2VudCcgfCAnY3JpdGljYWwnID0gJ2dlbnRsZSc7XG4gICAgICAgICAgaWYgKGxvd2VyTGluZS5pbmNsdWRlcygndXJnZW50JykpIHR5cGUgPSAndXJnZW50JztcbiAgICAgICAgICBpZiAobG93ZXJMaW5lLmluY2x1ZGVzKCdjcml0aWNhbCcpKSB0eXBlID0gJ2NyaXRpY2FsJztcbiAgICAgICAgICBcbiAgICAgICAgICByZW1pbmRlcnMucHVzaCh7XG4gICAgICAgICAgICBzY2hlZHVsZWRGb3IsXG4gICAgICAgICAgICBtZXNzYWdlOiBsaW5lLnRyaW0oKSxcbiAgICAgICAgICAgIHR5cGUsXG4gICAgICAgICAgICBlc2NhbGF0ZTogdHlwZSAhPT0gJ2dlbnRsZSdcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIElmIG5vIHJlbWluZGVycyBleHRyYWN0ZWQsIGNyZWF0ZSBkZWZhdWx0IG9uZXNcbiAgICBpZiAocmVtaW5kZXJzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgICAgIFxuICAgICAgLy8gR2VudGxlIHJlbWluZGVyIGluIDQgaG91cnNcbiAgICAgIGNvbnN0IGdlbnRsZVJlbWluZGVyID0gbmV3IERhdGUobm93KTtcbiAgICAgIGdlbnRsZVJlbWluZGVyLnNldEhvdXJzKGdlbnRsZVJlbWluZGVyLmdldEhvdXJzKCkgKyA0KTtcbiAgICAgIHJlbWluZGVycy5wdXNoKHtcbiAgICAgICAgc2NoZWR1bGVkRm9yOiBnZW50bGVSZW1pbmRlcixcbiAgICAgICAgbWVzc2FnZTogJ0dlbnRsZSByZW1pbmRlcjogRG9jdW1lbnQgcmVxdWlyZXMgeW91ciBhdHRlbnRpb24nLFxuICAgICAgICB0eXBlOiAnZ2VudGxlJyxcbiAgICAgICAgZXNjYWxhdGU6IGZhbHNlXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gVXJnZW50IHJlbWluZGVyIGluIDI0IGhvdXJzXG4gICAgICBjb25zdCB1cmdlbnRSZW1pbmRlciA9IG5ldyBEYXRlKG5vdyk7XG4gICAgICB1cmdlbnRSZW1pbmRlci5zZXRIb3Vycyh1cmdlbnRSZW1pbmRlci5nZXRIb3VycygpICsgMjQpO1xuICAgICAgcmVtaW5kZXJzLnB1c2goe1xuICAgICAgICBzY2hlZHVsZWRGb3I6IHVyZ2VudFJlbWluZGVyLFxuICAgICAgICBtZXNzYWdlOiAnVXJnZW50IHJlbWluZGVyOiBEb2N1bWVudCBkZWFkbGluZSBhcHByb2FjaGluZycsXG4gICAgICAgIHR5cGU6ICd1cmdlbnQnLFxuICAgICAgICBlc2NhbGF0ZTogdHJ1ZVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlbWluZGVycyxcbiAgICAgIGVzY2FsYXRpb25QbGFuXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZW5lcmF0ZSBsb2NhbCByZW1pbmRlciBzY2hlZHVsZSBmYWxsYmFja1xuICAgKi9cbiAgcHJpdmF0ZSBzdGF0aWMgZ2VuZXJhdGVMb2NhbFJlbWluZGVyU2NoZWR1bGUoXG4gICAgZG9jdW1lbnQ6IERvY3VtZW50RGF0YSxcbiAgICB1c2VyOiBVc2VyRGF0YSxcbiAgICBkZWFkbGluZT86IERhdGVcbiAgKTogUmVtaW5kZXJTY2hlZHVsZVJlc3VsdCB7XG4gICAgY29uc3QgcmVtaW5kZXJzOiBSZW1pbmRlckVudHJ5W10gPSBbXTtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIHRpbWUgdW50aWwgZGVhZGxpbmVcbiAgICBjb25zdCB0aW1lVW50aWxEZWFkbGluZSA9IGRlYWRsaW5lID8gZGVhZGxpbmUuZ2V0VGltZSgpIC0gbm93LmdldFRpbWUoKSA6IG51bGw7XG4gICAgY29uc3QgZGF5c1VudGlsRGVhZGxpbmUgPSB0aW1lVW50aWxEZWFkbGluZSA/IE1hdGguY2VpbCh0aW1lVW50aWxEZWFkbGluZSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkgOiBudWxsO1xuXG4gICAgLy8gRGV0ZXJtaW5lIHVyZ2VuY3kgYmFzZWQgb24gZG9jdW1lbnQgY2F0ZWdvcnkgYW5kIGRlYWRsaW5lXG4gICAgY29uc3QgaXNVcmdlbnQgPSBkb2N1bWVudC5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCd1cmdlbnQnKSB8fCBcbiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYWN0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdpbW1lZGlhdGUnKSB8fFxuICAgICAgICAgICAgICAgICAgICAoZGF5c1VudGlsRGVhZGxpbmUgIT09IG51bGwgJiYgZGF5c1VudGlsRGVhZGxpbmUgPD0gMSk7XG5cbiAgICBpZiAoZGVhZGxpbmUgJiYgZGF5c1VudGlsRGVhZGxpbmUgIT09IG51bGwpIHtcbiAgICAgIGlmIChkYXlzVW50aWxEZWFkbGluZSA+IDcpIHtcbiAgICAgICAgLy8gTG9uZyBkZWFkbGluZSAtIHNwcmVhZCBvdXQgcmVtaW5kZXJzXG4gICAgICAgIFxuICAgICAgICAvLyBGaXJzdCBnZW50bGUgcmVtaW5kZXIgaW4gMiBkYXlzXG4gICAgICAgIGNvbnN0IGZpcnN0UmVtaW5kZXIgPSBuZXcgRGF0ZShub3cpO1xuICAgICAgICBmaXJzdFJlbWluZGVyLnNldERhdGUoZmlyc3RSZW1pbmRlci5nZXREYXRlKCkgKyAyKTtcbiAgICAgICAgZmlyc3RSZW1pbmRlci5zZXRIb3Vycyg5LCAwLCAwLCAwKTsgLy8gOSBBTVxuICAgICAgICByZW1pbmRlcnMucHVzaCh7XG4gICAgICAgICAgc2NoZWR1bGVkRm9yOiBmaXJzdFJlbWluZGVyLFxuICAgICAgICAgIG1lc3NhZ2U6IGBHZW50bGUgcmVtaW5kZXI6IFwiJHtkb2N1bWVudC50aXRsZX1cIiByZXF1aXJlcyB5b3VyIGF0dGVudGlvbmAsXG4gICAgICAgICAgdHlwZTogJ2dlbnRsZScsXG4gICAgICAgICAgZXNjYWxhdGU6IGZhbHNlXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFNlY29uZCByZW1pbmRlciAzIGRheXMgYmVmb3JlIGRlYWRsaW5lXG4gICAgICAgIGNvbnN0IHNlY29uZFJlbWluZGVyID0gbmV3IERhdGUoZGVhZGxpbmUpO1xuICAgICAgICBzZWNvbmRSZW1pbmRlci5zZXREYXRlKHNlY29uZFJlbWluZGVyLmdldERhdGUoKSAtIDMpO1xuICAgICAgICBzZWNvbmRSZW1pbmRlci5zZXRIb3VycygxNCwgMCwgMCwgMCk7IC8vIDIgUE1cbiAgICAgICAgcmVtaW5kZXJzLnB1c2goe1xuICAgICAgICAgIHNjaGVkdWxlZEZvcjogc2Vjb25kUmVtaW5kZXIsXG4gICAgICAgICAgbWVzc2FnZTogYFJlbWluZGVyOiBcIiR7ZG9jdW1lbnQudGl0bGV9XCIgZGVhZGxpbmUgaW4gMyBkYXlzYCxcbiAgICAgICAgICB0eXBlOiAndXJnZW50JyxcbiAgICAgICAgICBlc2NhbGF0ZTogZmFsc2VcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gRmluYWwgdXJnZW50IHJlbWluZGVyIDEgZGF5IGJlZm9yZSBkZWFkbGluZVxuICAgICAgICBjb25zdCBmaW5hbFJlbWluZGVyID0gbmV3IERhdGUoZGVhZGxpbmUpO1xuICAgICAgICBmaW5hbFJlbWluZGVyLnNldERhdGUoZmluYWxSZW1pbmRlci5nZXREYXRlKCkgLSAxKTtcbiAgICAgICAgZmluYWxSZW1pbmRlci5zZXRIb3VycygxMCwgMCwgMCwgMCk7IC8vIDEwIEFNXG4gICAgICAgIHJlbWluZGVycy5wdXNoKHtcbiAgICAgICAgICBzY2hlZHVsZWRGb3I6IGZpbmFsUmVtaW5kZXIsXG4gICAgICAgICAgbWVzc2FnZTogYFVSR0VOVDogXCIke2RvY3VtZW50LnRpdGxlfVwiIGRlYWRsaW5lIHRvbW9ycm93IWAsXG4gICAgICAgICAgdHlwZTogJ2NyaXRpY2FsJyxcbiAgICAgICAgICBlc2NhbGF0ZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSBpZiAoZGF5c1VudGlsRGVhZGxpbmUgPiAyKSB7XG4gICAgICAgIC8vIE1lZGl1bSBkZWFkbGluZSAtIGZld2VyIHJlbWluZGVyc1xuICAgICAgICBcbiAgICAgICAgLy8gVXJnZW50IHJlbWluZGVyIGluIDQgaG91cnNcbiAgICAgICAgY29uc3QgdXJnZW50UmVtaW5kZXIgPSBuZXcgRGF0ZShub3cpO1xuICAgICAgICB1cmdlbnRSZW1pbmRlci5zZXRIb3Vycyh1cmdlbnRSZW1pbmRlci5nZXRIb3VycygpICsgNCk7XG4gICAgICAgIHJlbWluZGVycy5wdXNoKHtcbiAgICAgICAgICBzY2hlZHVsZWRGb3I6IHVyZ2VudFJlbWluZGVyLFxuICAgICAgICAgIG1lc3NhZ2U6IGBJbXBvcnRhbnQ6IFwiJHtkb2N1bWVudC50aXRsZX1cIiBuZWVkcyBhdHRlbnRpb24gLSBkZWFkbGluZSBpbiAke2RheXNVbnRpbERlYWRsaW5lfSBkYXlzYCxcbiAgICAgICAgICB0eXBlOiAndXJnZW50JyxcbiAgICAgICAgICBlc2NhbGF0ZTogZmFsc2VcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gQ3JpdGljYWwgcmVtaW5kZXIgMSBkYXkgYmVmb3JlIGRlYWRsaW5lXG4gICAgICAgIGNvbnN0IGNyaXRpY2FsUmVtaW5kZXIgPSBuZXcgRGF0ZShkZWFkbGluZSk7XG4gICAgICAgIGNyaXRpY2FsUmVtaW5kZXIuc2V0RGF0ZShjcml0aWNhbFJlbWluZGVyLmdldERhdGUoKSAtIDEpO1xuICAgICAgICBjcml0aWNhbFJlbWluZGVyLnNldEhvdXJzKDksIDAsIDAsIDApO1xuICAgICAgICByZW1pbmRlcnMucHVzaCh7XG4gICAgICAgICAgc2NoZWR1bGVkRm9yOiBjcml0aWNhbFJlbWluZGVyLFxuICAgICAgICAgIG1lc3NhZ2U6IGBDUklUSUNBTDogXCIke2RvY3VtZW50LnRpdGxlfVwiIGRlYWRsaW5lIHRvbW9ycm93IWAsXG4gICAgICAgICAgdHlwZTogJ2NyaXRpY2FsJyxcbiAgICAgICAgICBlc2NhbGF0ZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIFNob3J0IGRlYWRsaW5lIC0gaW1tZWRpYXRlIGFjdGlvbiBuZWVkZWRcbiAgICAgICAgXG4gICAgICAgIC8vIEltbWVkaWF0ZSB1cmdlbnQgcmVtaW5kZXJcbiAgICAgICAgY29uc3QgaW1tZWRpYXRlUmVtaW5kZXIgPSBuZXcgRGF0ZShub3cpO1xuICAgICAgICBpbW1lZGlhdGVSZW1pbmRlci5zZXRIb3VycyhpbW1lZGlhdGVSZW1pbmRlci5nZXRIb3VycygpICsgMSk7XG4gICAgICAgIHJlbWluZGVycy5wdXNoKHtcbiAgICAgICAgICBzY2hlZHVsZWRGb3I6IGltbWVkaWF0ZVJlbWluZGVyLFxuICAgICAgICAgIG1lc3NhZ2U6IGBVUkdFTlQ6IFwiJHtkb2N1bWVudC50aXRsZX1cIiByZXF1aXJlcyBpbW1lZGlhdGUgYXR0ZW50aW9uIC0gZGVhZGxpbmUgc29vbiFgLFxuICAgICAgICAgIHR5cGU6ICdjcml0aWNhbCcsXG4gICAgICAgICAgZXNjYWxhdGU6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIE5vIGRlYWRsaW5lIHNwZWNpZmllZCAtIHVzZSBzdGFuZGFyZCByZW1pbmRlcnNcbiAgICAgIFxuICAgICAgaWYgKGlzVXJnZW50KSB7XG4gICAgICAgIC8vIFVyZ2VudCBkb2N1bWVudCAtIHF1aWNrIHJlbWluZGVyc1xuICAgICAgICBjb25zdCB1cmdlbnRSZW1pbmRlciA9IG5ldyBEYXRlKG5vdyk7XG4gICAgICAgIHVyZ2VudFJlbWluZGVyLnNldEhvdXJzKHVyZ2VudFJlbWluZGVyLmdldEhvdXJzKCkgKyAyKTtcbiAgICAgICAgcmVtaW5kZXJzLnB1c2goe1xuICAgICAgICAgIHNjaGVkdWxlZEZvcjogdXJnZW50UmVtaW5kZXIsXG4gICAgICAgICAgbWVzc2FnZTogYFVSR0VOVDogXCIke2RvY3VtZW50LnRpdGxlfVwiIHJlcXVpcmVzIGltbWVkaWF0ZSBhY3Rpb25gLFxuICAgICAgICAgIHR5cGU6ICd1cmdlbnQnLFxuICAgICAgICAgIGVzY2FsYXRlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gUmVndWxhciBkb2N1bWVudCAtIHN0YW5kYXJkIHNjaGVkdWxlXG4gICAgICAgIGNvbnN0IGdlbnRsZVJlbWluZGVyID0gbmV3IERhdGUobm93KTtcbiAgICAgICAgZ2VudGxlUmVtaW5kZXIuc2V0SG91cnMoZ2VudGxlUmVtaW5kZXIuZ2V0SG91cnMoKSArIDQpO1xuICAgICAgICByZW1pbmRlcnMucHVzaCh7XG4gICAgICAgICAgc2NoZWR1bGVkRm9yOiBnZW50bGVSZW1pbmRlcixcbiAgICAgICAgICBtZXNzYWdlOiBgUmVtaW5kZXI6IFwiJHtkb2N1bWVudC50aXRsZX1cIiBpcyB3YWl0aW5nIGZvciB5b3VyIGF0dGVudGlvbmAsXG4gICAgICAgICAgdHlwZTogJ2dlbnRsZScsXG4gICAgICAgICAgZXNjYWxhdGU6IGZhbHNlXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnN0IGZvbGxvd1VwUmVtaW5kZXIgPSBuZXcgRGF0ZShub3cpO1xuICAgICAgICBmb2xsb3dVcFJlbWluZGVyLnNldERhdGUoZm9sbG93VXBSZW1pbmRlci5nZXREYXRlKCkgKyAxKTtcbiAgICAgICAgZm9sbG93VXBSZW1pbmRlci5zZXRIb3VycygxMCwgMCwgMCwgMCk7XG4gICAgICAgIHJlbWluZGVycy5wdXNoKHtcbiAgICAgICAgICBzY2hlZHVsZWRGb3I6IGZvbGxvd1VwUmVtaW5kZXIsXG4gICAgICAgICAgbWVzc2FnZTogYEZvbGxvdy11cDogXCIke2RvY3VtZW50LnRpdGxlfVwiIHN0aWxsIG5lZWRzIHByb2Nlc3NpbmdgLFxuICAgICAgICAgIHR5cGU6ICd1cmdlbnQnLFxuICAgICAgICAgIGVzY2FsYXRlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEVzY2FsYXRpb24gcGxhbiBiYXNlZCBvbiB1c2VyIHJvbGVcbiAgICBjb25zdCBlc2NhbGF0aW9uUGxhbiA9IHtcbiAgICAgIGVzY2FsYXRlQWZ0ZXI6IHVzZXIucm9sZSA9PT0gJ1JFR0lPTkFMX0RJUkVDVE9SJyA/ICc0IGhvdXJzJyA6ICcyNCBob3VycycsXG4gICAgICBlc2NhbGF0ZVRvOiB1c2VyLnJvbGUgPT09ICdFTVBMT1lFRScgPyAnZGl2aXNpb24gY2hpZWYnIDogJ3JlZ2lvbmFsIGRpcmVjdG9yJyxcbiAgICAgIGVzY2FsYXRpb25NZXNzYWdlOiBgRG9jdW1lbnQgXCIke2RvY3VtZW50LnRpdGxlfVwiIHJlcXVpcmVzIGltbWVkaWF0ZSBhdHRlbnRpb24gZnJvbSAke3VzZXIubmFtZX0gaW4gJHt1c2VyLmRpdmlzaW9ufWBcbiAgICB9O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlbWluZGVycyxcbiAgICAgIGVzY2FsYXRpb25QbGFuXG4gICAgfTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImdlbWluaUNsaWVudCIsIlJlbWluZGVyU2VydmljZSIsImdlbmVyYXRlUmVtaW5kZXJTY2hlZHVsZSIsImRvY3VtZW50IiwidXNlciIsImRlYWRsaW5lIiwicHJvbXB0IiwiY3JlYXRlUmVtaW5kZXJQcm9tcHQiLCJpc0F2YWlsYWJsZSIsInJlc3BvbnNlIiwiZ2VuZXJhdGVDb250ZW50IiwicGFyc2VSZW1pbmRlclNjaGVkdWxlIiwiYXBpRXJyb3IiLCJjb25zb2xlIiwid2FybiIsImdlbmVyYXRlTG9jYWxSZW1pbmRlclNjaGVkdWxlIiwiZXJyb3IiLCJ0aXRsZSIsImNhdGVnb3J5Iiwic3RhdHVzIiwiY3JlYXRlZEF0IiwiYWN0aW9uIiwibmFtZSIsImRpdmlzaW9uIiwicm9sZSIsInRvSVNPU3RyaW5nIiwicGFyc2VkIiwiSlNPTiIsInBhcnNlIiwicmVtaW5kZXJzIiwibWFwIiwicmVtaW5kZXIiLCJzY2hlZHVsZWRGb3IiLCJEYXRlIiwibWVzc2FnZSIsInR5cGUiLCJlc2NhbGF0ZSIsImVzY2FsYXRpb25QbGFuIiwiZXNjYWxhdGVBZnRlciIsImVzY2FsYXRlVG8iLCJlc2NhbGF0aW9uTWVzc2FnZSIsImV4dHJhY3RSZW1pbmRlckZyb21UZXh0IiwibGluZXMiLCJzcGxpdCIsImZpbHRlciIsImxpbmUiLCJ0cmltIiwibG93ZXJMaW5lIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImhvdXJzTWF0Y2giLCJtYXRjaCIsImhvdXJzIiwicGFyc2VJbnQiLCJzZXRIb3VycyIsImdldEhvdXJzIiwicHVzaCIsImxlbmd0aCIsIm5vdyIsImdlbnRsZVJlbWluZGVyIiwidXJnZW50UmVtaW5kZXIiLCJ0aW1lVW50aWxEZWFkbGluZSIsImdldFRpbWUiLCJkYXlzVW50aWxEZWFkbGluZSIsIk1hdGgiLCJjZWlsIiwiaXNVcmdlbnQiLCJmaXJzdFJlbWluZGVyIiwic2V0RGF0ZSIsImdldERhdGUiLCJzZWNvbmRSZW1pbmRlciIsImZpbmFsUmVtaW5kZXIiLCJjcml0aWNhbFJlbWluZGVyIiwiaW1tZWRpYXRlUmVtaW5kZXIiLCJmb2xsb3dVcFJlbWluZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/services/reminderService.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/AuditLog.ts":
/*!********************************!*\
  !*** ./src/models/AuditLog.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n\n\nconst AuditLogSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        enum: Object.values(_types_audit__WEBPACK_IMPORTED_MODULE_1__.AuditLogAction),\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    performedBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user who performed the action\"\n        ]\n    },\n    targetId: {\n        type: String\n    },\n    targetType: {\n        type: String\n    },\n    details: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n    },\n    ipAddress: {\n        type: String\n    },\n    userAgent: {\n        type: String\n    }\n}, {\n    timestamps: true\n});\n// Create a text index for searching\nAuditLogSchema.index({\n    action: \"text\",\n    targetType: \"text\"\n});\n// Fix for \"Cannot read properties of undefined (reading 'AuditLog')\" error\n// Check if the model exists in mongoose.models before trying to access it\nlet AuditLog;\ntry {\n    // Try to get the existing model\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\", AuditLogSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuditLog);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/AuditLog.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst SessionInfoSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    token: {\n        type: String,\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    userAgent: {\n        type: String\n    },\n    ipAddress: {\n        type: String\n    }\n});\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a name\"\n        ],\n        maxlength: [\n            60,\n            \"Name cannot be more than 60 characters\"\n        ],\n        unique: true,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: false,\n        lowercase: true,\n        trim: true,\n        unique: true,\n        sparse: true\n    },\n    password: {\n        type: String,\n        select: false\n    },\n    role: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.UserRole),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.UserRole.EMPLOYEE\n    },\n    division: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a division\"\n        ]\n    },\n    image: {\n        type: String\n    },\n    activeSessions: {\n        type: [\n            SessionInfoSchema\n        ],\n        default: []\n    }\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet User;\ntry {\n    // Try to get the existing model\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (User);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/audit.ts":
/*!****************************!*\
  !*** ./src/types/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLogAction: () => (/* binding */ AuditLogAction)\n/* harmony export */ });\nvar AuditLogAction;\n(function(AuditLogAction) {\n    AuditLogAction[\"USER_CREATED\"] = \"USER_CREATED\";\n    AuditLogAction[\"USER_UPDATED\"] = \"USER_UPDATED\";\n    AuditLogAction[\"USER_DELETED\"] = \"USER_DELETED\";\n    AuditLogAction[\"USER_LOGIN\"] = \"USER_LOGIN\";\n    AuditLogAction[\"DOCUMENT_CREATED\"] = \"DOCUMENT_CREATED\";\n    AuditLogAction[\"DOCUMENT_UPDATED\"] = \"DOCUMENT_UPDATED\";\n    AuditLogAction[\"DOCUMENT_DELETED\"] = \"DOCUMENT_DELETED\";\n    AuditLogAction[\"DOCUMENT_STATUS_CHANGED\"] = \"DOCUMENT_STATUS_CHANGED\";\n    AuditLogAction[\"DOCUMENT_SHARED\"] = \"DOCUMENT_SHARED\";\n    AuditLogAction[\"DOCUMENT_FORWARDED\"] = \"DOCUMENT_FORWARDED\";\n    AuditLogAction[\"DOCUMENT_RECEIVED\"] = \"DOCUMENT_RECEIVED\";\n    AuditLogAction[\"DOCUMENT_PROCESSED\"] = \"DOCUMENT_PROCESSED\";\n    AuditLogAction[\"PROFILE_CHANGE_REQUESTED\"] = \"PROFILE_CHANGE_REQUESTED\";\n    AuditLogAction[\"PROFILE_CHANGE_APPROVED\"] = \"PROFILE_CHANGE_APPROVED\";\n    AuditLogAction[\"PROFILE_CHANGE_REJECTED\"] = \"PROFILE_CHANGE_REJECTED\";\n    AuditLogAction[\"FILE_UPLOADED\"] = \"FILE_UPLOADED\";\n    AuditLogAction[\"FILE_DOWNLOADED\"] = \"FILE_DOWNLOADED\";\n    AuditLogAction[\"FILE_DELETED\"] = \"FILE_DELETED\";\n    AuditLogAction[\"FILE_ACCESSED\"] = \"FILE_ACCESSED\";\n    AuditLogAction[\"FEEDBACK_CREATED\"] = \"FEEDBACK_CREATED\";\n    AuditLogAction[\"FEEDBACK_UPDATED\"] = \"FEEDBACK_UPDATED\";\n    AuditLogAction[\"FEEDBACK_DELETED\"] = \"FEEDBACK_DELETED\";\n    AuditLogAction[\"FEEDBACK_STATUS_CHANGED\"] = \"FEEDBACK_STATUS_CHANGED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_GENERATED\"] = \"FEEDBACK_AI_SUGGESTION_GENERATED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_REMOVED\"] = \"FEEDBACK_AI_SUGGESTION_REMOVED\";\n    AuditLogAction[\"SEARCH_PERFORMED\"] = \"SEARCH_PERFORMED\";\n    AuditLogAction[\"DATA_CLEANUP\"] = \"DATA_CLEANUP\";\n    AuditLogAction[\"SYSTEM_MAINTENANCE\"] = \"SYSTEM_MAINTENANCE\";\n    AuditLogAction[\"ARCHIVE_EXPORTED\"] = \"ARCHIVE_EXPORTED\";\n})(AuditLogAction || (AuditLogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Division: () => (/* binding */ Division),\n/* harmony export */   DocumentAction: () => (/* binding */ DocumentAction),\n/* harmony export */   DocumentCategory: () => (/* binding */ DocumentCategory),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   FeedbackCategory: () => (/* binding */ FeedbackCategory),\n/* harmony export */   FeedbackStatus: () => (/* binding */ FeedbackStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n// User Roles\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    UserRole[\"REGIONAL_DIRECTOR\"] = \"REGIONAL_DIRECTOR\";\n    UserRole[\"DIVISION_CHIEF\"] = \"DIVISION_CHIEF\";\n    UserRole[\"EMPLOYEE\"] = \"EMPLOYEE\";\n})(UserRole || (UserRole = {}));\nvar Division;\n(function(Division) {\n    Division[\"ORD\"] = \"ORD\";\n    Division[\"FAD\"] = \"FAD\";\n    Division[\"MMD\"] = \"MMD\";\n    Division[\"MSESDD\"] = \"MSESDD\";\n    Division[\"GSD\"] = \"GSD\";\n})(Division || (Division = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"INBOX\"] = \"INBOX\";\n    DocumentStatus[\"SENT\"] = \"SENT\";\n    DocumentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    DocumentStatus[\"FORWARDED\"] = \"FORWARDED\";\n    DocumentStatus[\"PENDING\"] = \"PENDING\";\n    DocumentStatus[\"PROCESSED\"] = \"PROCESSED\";\n    DocumentStatus[\"ARCHIVED\"] = \"ARCHIVED\"; // Document has been archived for storage\n})(DocumentStatus || (DocumentStatus = {}));\nvar DocumentCategory;\n(function(DocumentCategory) {\n    // Common Office Documents\n    DocumentCategory[\"MEMO\"] = \"MEMO\";\n    DocumentCategory[\"LETTER\"] = \"LETTER\";\n    DocumentCategory[\"REPORT\"] = \"REPORT\";\n    DocumentCategory[\"PROPOSAL\"] = \"PROPOSAL\";\n    DocumentCategory[\"MINUTES\"] = \"MINUTES\";\n    DocumentCategory[\"FORM\"] = \"FORM\";\n    // Official Documents\n    DocumentCategory[\"CIRCULAR\"] = \"CIRCULAR\";\n    DocumentCategory[\"ADVISORY\"] = \"ADVISORY\";\n    DocumentCategory[\"BULLETIN\"] = \"BULLETIN\";\n    DocumentCategory[\"NOTICE\"] = \"NOTICE\";\n    DocumentCategory[\"ANNOUNCEMENT\"] = \"ANNOUNCEMENT\";\n    DocumentCategory[\"RESOLUTION\"] = \"RESOLUTION\";\n    DocumentCategory[\"POLICY\"] = \"POLICY\";\n    DocumentCategory[\"GUIDELINE\"] = \"GUIDELINE\";\n    DocumentCategory[\"DIRECTIVE\"] = \"DIRECTIVE\";\n    DocumentCategory[\"MEMORANDUM_ORDER\"] = \"MEMORANDUM ORDER\";\n    DocumentCategory[\"MEMORANDUM_CIRCULAR\"] = \"MEMORANDUM CIRCULAR\";\n    DocumentCategory[\"EXECUTIVE_ORDER\"] = \"EXECUTIVE ORDER\";\n    DocumentCategory[\"ADMINISTRATIVE_ORDER\"] = \"ADMINISTRATIVE ORDER\";\n    // Legal & Financial Documents\n    DocumentCategory[\"CONTRACT\"] = \"CONTRACT\";\n    DocumentCategory[\"CERTIFICATE\"] = \"CERTIFICATE\";\n    DocumentCategory[\"ENDORSEMENT\"] = \"ENDORSEMENT\";\n    DocumentCategory[\"MANUAL\"] = \"MANUAL\";\n    DocumentCategory[\"INVOICE\"] = \"INVOICE\";\n    DocumentCategory[\"RECEIPT\"] = \"RECEIPT\";\n    DocumentCategory[\"VOUCHER\"] = \"VOUCHER\";\n    DocumentCategory[\"REQUISITION\"] = \"REQUISITION\";\n    DocumentCategory[\"PURCHASE_ORDER\"] = \"PURCHASE ORDER\";\n    DocumentCategory[\"BUDGET_REQUEST\"] = \"BUDGET REQUEST\";\n    DocumentCategory[\"TRAVEL_ORDER\"] = \"TRAVEL ORDER\";\n    DocumentCategory[\"LEAVE_FORM\"] = \"LEAVE FORM\";\n    // Other\n    DocumentCategory[\"OTHER\"] = \"OTHER\";\n})(DocumentCategory || (DocumentCategory = {}));\nvar DocumentAction;\n(function(DocumentAction) {\n    DocumentAction[\"NONE\"] = \"No specific action required\";\n    // Actions from the image (A-S)\n    DocumentAction[\"FOR_INFO\"] = \"A - For information/guidance/reference\";\n    DocumentAction[\"FOR_COMMENTS\"] = \"B - For comments/recommendations\";\n    DocumentAction[\"TAKE_UP\"] = \"C - Pls. take up with me\";\n    DocumentAction[\"DRAFT_ANSWER\"] = \"D - Pls. draft answer/memo/acknow.\";\n    DocumentAction[\"FOR_ACTION\"] = \"E - For appropriate action\";\n    DocumentAction[\"IMMEDIATE_INVESTIGATION\"] = \"F - Pls. immediate investigation\";\n    DocumentAction[\"ATTACH_SUPPORTING\"] = \"G - Pls. attach supporting papers\";\n    DocumentAction[\"FOR_APPROVAL\"] = \"H - For approval\";\n    DocumentAction[\"FOR_SIGNATURE\"] = \"I - For initial/signature\";\n    DocumentAction[\"STUDY_EVALUATE\"] = \"J - Pls. study / evaluate\";\n    DocumentAction[\"RELEASE_FILE\"] = \"K - Pls. release/file\";\n    DocumentAction[\"UPDATE_STATUS\"] = \"L - Update status of case\";\n    DocumentAction[\"FILE_CLOSE\"] = \"M - Filed / Close\";\n    DocumentAction[\"FOR_ADA\"] = \"N - For ADA / Check Preparation\";\n    DocumentAction[\"FOR_DISCUSSION\"] = \"O - FOD (For Discussion)\";\n    DocumentAction[\"FOR_REVISION\"] = \"P - For Revision\";\n    DocumentAction[\"ATTACH_DRAFT\"] = \"Q - Pls. Attach Draft File\";\n    DocumentAction[\"SAVED\"] = \"R - Saved\";\n    DocumentAction[\"FOR_SCANNING\"] = \"S - For Scanning\";\n    // Additional useful actions for office work\n    DocumentAction[\"URGENT\"] = \"URGENT - Requires immediate attention\";\n    DocumentAction[\"CONFIDENTIAL\"] = \"CONFIDENTIAL - Restricted access\";\n    DocumentAction[\"FOR_REVIEW\"] = \"FOR REVIEW - Please review and provide feedback\";\n    DocumentAction[\"FOR_COORDINATION\"] = \"FOR COORDINATION - Coordinate with relevant departments\";\n    DocumentAction[\"FOR_COMPLIANCE\"] = \"FOR COMPLIANCE - Ensure compliance with regulations\";\n    DocumentAction[\"FOR_IMPLEMENTATION\"] = \"FOR IMPLEMENTATION - Implement the described actions\";\n    DocumentAction[\"FOR_FILING\"] = \"FOR FILING - File for future reference\";\n    DocumentAction[\"FOR_DISTRIBUTION\"] = \"FOR DISTRIBUTION - Distribute to concerned parties\";\n    DocumentAction[\"FOR_ENDORSEMENT\"] = \"FOR ENDORSEMENT - Endorse to appropriate authority\";\n    DocumentAction[\"FOR_VERIFICATION\"] = \"FOR VERIFICATION - Verify information/data\";\n    DocumentAction[\"FOR_RECORDING\"] = \"FOR RECORDING - Record in the system\";\n})(DocumentAction || (DocumentAction = {}));\nvar FeedbackCategory;\n(function(FeedbackCategory) {\n    FeedbackCategory[\"BUG\"] = \"bug\";\n    FeedbackCategory[\"FEATURE\"] = \"feature\";\n    FeedbackCategory[\"IMPROVEMENT\"] = \"improvement\";\n    FeedbackCategory[\"OTHER\"] = \"other\";\n})(FeedbackCategory || (FeedbackCategory = {}));\nvar FeedbackStatus;\n(function(FeedbackStatus) {\n    FeedbackStatus[\"PENDING\"] = \"pending\";\n    FeedbackStatus[\"REVIEWED\"] = \"reviewed\";\n    FeedbackStatus[\"IMPLEMENTED\"] = \"implemented\";\n    FeedbackStatus[\"REJECTED\"] = \"rejected\";\n})(FeedbackStatus || (FeedbackStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/audit.ts":
/*!****************************!*\
  !*** ./src/utils/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent)\n/* harmony export */ });\n/* harmony import */ var _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/AuditLog */ \"(rsc)/./src/models/AuditLog.ts\");\n\nasync function logAuditEvent({ action, performedBy, targetId, targetType, details, request }) {\n    try {\n        const auditLogData = {\n            action,\n            performedBy,\n            targetId,\n            targetType,\n            details\n        };\n        // Add request information if available\n        if (request) {\n            auditLogData.ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n            auditLogData.userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        }\n        // Create the audit log entry\n        await _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create(auditLogData);\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n    // Don't throw the error - we don't want to break the main functionality\n    // if audit logging fails\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/serverTimestamp.ts":
/*!**************************************!*\
  !*** ./src/utils/serverTimestamp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerTimestamp: () => (/* binding */ getServerTimestamp)\n/* harmony export */ });\n/**\n * This utility manages the server timestamp used to invalidate sessions on server restart\n */ // Use a more stable identifier that doesn't change on every development hot reload\n// In production, this will still change on server restart\nlet SERVER_START_TIMESTAMP;\n// Try to use a timestamp that persists across hot reloads in development\nif (true) {\n    // In development, use a timestamp that changes daily instead of on every restart\n    // This prevents constant logouts during development\n    const today = new Date();\n    const dateString = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;\n    SERVER_START_TIMESTAMP = dateString;\n} else {}\n// Function to get the current server timestamp\nfunction getServerTimestamp() {\n    return SERVER_START_TIMESTAMP;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/serverTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/sessionToken.ts":
/*!***********************************!*\
  !*** ./src/utils/sessionToken.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAllExpiredSessions: () => (/* binding */ cleanupAllExpiredSessions),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   removeAllOtherSessions: () => (/* binding */ removeAllOtherSessions),\n/* harmony export */   removeSession: () => (/* binding */ removeSession),\n/* harmony export */   validateSession: () => (/* binding */ validateSession)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n\n\n\n// Maximum number of sessions per user\nconst MAX_SESSIONS_PER_USER = 5;\n// Session expiration time in milliseconds (24 hours)\nconst SESSION_EXPIRATION_MS = 24 * 60 * 60 * 1000;\n/**\n * Generate a unique session token\n * @returns A random session token\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Create a new session for a user\n * @param userId The user ID\n * @param userAgent The user agent string\n * @param ipAddress The IP address\n * @returns The session token\n */ async function createSession(userId, userAgent, ipAddress) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const sessionToken = generateSessionToken();\n    const now = new Date();\n    const sessionInfo = {\n        token: sessionToken,\n        createdAt: now,\n        lastActive: now,\n        userAgent,\n        ipAddress\n    };\n    // Get the user with their current sessions\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // If the user has too many active sessions, remove the oldest ones\n    if (user.activeSessions && user.activeSessions.length >= MAX_SESSIONS_PER_USER) {\n        // Sort sessions by lastActive (oldest first)\n        const sortedSessions = [\n            ...user.activeSessions\n        ].sort((a, b)=>a.lastActive.getTime() - b.lastActive.getTime());\n        // Calculate how many sessions to remove\n        const sessionsToRemove = Math.max(0, sortedSessions.length - MAX_SESSIONS_PER_USER + 1);\n        if (sessionsToRemove > 0) {\n            // Get the tokens of the oldest sessions\n            const tokensToRemove = sortedSessions.slice(0, sessionsToRemove).map((session)=>session.token);\n            // Remove the oldest sessions\n            await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n                _id: userId\n            }, {\n                $pull: {\n                    activeSessions: {\n                        token: {\n                            $in: tokensToRemove\n                        }\n                    }\n                }\n            });\n        }\n    }\n    // Add the new session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndUpdate(userId, {\n        $push: {\n            activeSessions: sessionInfo\n        }\n    }, {\n        new: true\n    });\n    return sessionToken;\n}\n/**\n * Clean up expired sessions for a user\n * @param userId The user ID\n */ async function cleanupExpiredSessions(userId) {\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                lastActive: {\n                    $lt: expirationThreshold\n                }\n            }\n        }\n    });\n}\n/**\n * Validate a session token for a user\n * @param userId The user ID\n * @param sessionToken The session token to validate\n * @returns True if the session is valid, false otherwise\n */ async function validateSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // Find the user and check if they have this session token\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    });\n    if (!user) {\n        return false;\n    }\n    // Find the specific session\n    const session = user.activeSessions.find((s)=>s.token === sessionToken);\n    if (!session) {\n        return false;\n    }\n    // Check if the session has expired\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    if (session.lastActive < expirationThreshold) {\n        // Session has expired, remove it\n        await removeSession(userId, sessionToken);\n        return false;\n    }\n    // Update the last active time for this session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    }, {\n        $set: {\n            \"activeSessions.$.lastActive\": now\n        }\n    });\n    return true;\n}\n/**\n * Remove a session for a user\n * @param userId The user ID\n * @param sessionToken The session token to remove\n */ async function removeSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: sessionToken\n            }\n        }\n    });\n}\n/**\n * Get all active sessions for a user\n * @param userId The user ID\n * @returns Array of active sessions\n */ async function getUserSessions(userId) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    return user?.activeSessions || [];\n}\n/**\n * Remove all sessions for a user except the current one\n * @param userId The user ID\n * @param currentSessionToken The current session token to keep\n * @returns The number of sessions removed\n */ async function removeAllOtherSessions(userId, currentSessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // First clean up expired sessions\n    await cleanupExpiredSessions(userId);\n    // Get the user to count sessions before removal\n    const userBefore = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountBefore = userBefore?.activeSessions?.length || 0;\n    // Remove all other sessions\n    const result = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: {\n                    $ne: currentSessionToken\n                }\n            }\n        }\n    });\n    // Get the user again to count sessions after removal\n    const userAfter = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountAfter = userAfter?.activeSessions?.length || 0;\n    // Return the number of sessions removed\n    return sessionCountBefore - sessionCountAfter;\n}\n/**\n * Cleanup all expired sessions in the database\n * This can be run as a scheduled task\n */ async function cleanupAllExpiredSessions() {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    // Find all users with expired sessions\n    const users = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({\n        \"activeSessions.lastActive\": {\n            $lt: expirationThreshold\n        }\n    });\n    let totalRemoved = 0;\n    // Clean up expired sessions for each user\n    for (const user of users){\n        const sessionCountBefore = user.activeSessions.length;\n        // Remove expired sessions\n        await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n            _id: user._id\n        }, {\n            $pull: {\n                activeSessions: {\n                    lastActive: {\n                        $lt: expirationThreshold\n                    }\n                }\n            }\n        });\n        // Get the user again to count sessions after removal\n        const updatedUser = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(user._id);\n        const sessionCountAfter = updatedUser?.activeSessions?.length || 0;\n        totalRemoved += sessionCountBefore - sessionCountAfter;\n    }\n    return totalRemoved;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc2Vzc2lvblRva2VuLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUVKO0FBQ1E7QUFFekMsc0NBQXNDO0FBQ3RDLE1BQU1HLHdCQUF3QjtBQUU5QixxREFBcUQ7QUFDckQsTUFBTUMsd0JBQXdCLEtBQUssS0FBSyxLQUFLO0FBRTdDOzs7Q0FHQyxHQUNNLFNBQVNDO0lBQ2QsT0FBT0wsbURBQVdBLENBQUMsSUFBSU0sUUFBUSxDQUFDO0FBQ2xDO0FBRUE7Ozs7OztDQU1DLEdBQ00sZUFBZUMsY0FDcEJDLE1BQWMsRUFDZEMsU0FBa0IsRUFDbEJDLFNBQWtCO0lBRWxCLE1BQU1SLDJEQUFTQTtJQUVmLE1BQU1TLGVBQWVOO0lBQ3JCLE1BQU1PLE1BQU0sSUFBSUM7SUFFaEIsTUFBTUMsY0FBMkI7UUFDL0JDLE9BQU9KO1FBQ1BLLFdBQVdKO1FBQ1hLLFlBQVlMO1FBQ1pIO1FBQ0FDO0lBQ0Y7SUFFQSwyQ0FBMkM7SUFDM0MsTUFBTVEsT0FBTyxNQUFNakIsb0RBQUlBLENBQUNrQixRQUFRLENBQUNYO0lBRWpDLElBQUksQ0FBQ1UsTUFBTTtRQUNULE1BQU0sSUFBSUUsTUFBTTtJQUNsQjtJQUVBLGtDQUFrQztJQUNsQyxNQUFNQyx1QkFBdUJiO0lBRTdCLG1FQUFtRTtJQUNuRSxJQUFJVSxLQUFLSSxjQUFjLElBQUlKLEtBQUtJLGNBQWMsQ0FBQ0MsTUFBTSxJQUFJcEIsdUJBQXVCO1FBQzlFLDZDQUE2QztRQUM3QyxNQUFNcUIsaUJBQWlCO2VBQUlOLEtBQUtJLGNBQWM7U0FBQyxDQUFDRyxJQUFJLENBQ2xELENBQUNDLEdBQUdDLElBQU1ELEVBQUVULFVBQVUsQ0FBQ1csT0FBTyxLQUFLRCxFQUFFVixVQUFVLENBQUNXLE9BQU87UUFHekQsd0NBQXdDO1FBQ3hDLE1BQU1DLG1CQUFtQkMsS0FBS0MsR0FBRyxDQUFDLEdBQUdQLGVBQWVELE1BQU0sR0FBR3BCLHdCQUF3QjtRQUVyRixJQUFJMEIsbUJBQW1CLEdBQUc7WUFDeEIsd0NBQXdDO1lBQ3hDLE1BQU1HLGlCQUFpQlIsZUFDcEJTLEtBQUssQ0FBQyxHQUFHSixrQkFDVEssR0FBRyxDQUFDQyxDQUFBQSxVQUFXQSxRQUFRcEIsS0FBSztZQUUvQiw2QkFBNkI7WUFDN0IsTUFBTWQsb0RBQUlBLENBQUNtQyxTQUFTLENBQ2xCO2dCQUFFQyxLQUFLN0I7WUFBTyxHQUNkO2dCQUFFOEIsT0FBTztvQkFBRWhCLGdCQUFnQjt3QkFBRVAsT0FBTzs0QkFBRXdCLEtBQUtQO3dCQUFlO29CQUFFO2dCQUFFO1lBQUU7UUFFcEU7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNL0Isb0RBQUlBLENBQUN1QyxpQkFBaUIsQ0FDMUJoQyxRQUNBO1FBQ0VpQyxPQUFPO1lBQUVuQixnQkFBZ0JSO1FBQVk7SUFDdkMsR0FDQTtRQUFFNEIsS0FBSztJQUFLO0lBR2QsT0FBTy9CO0FBQ1Q7QUFFQTs7O0NBR0MsR0FDTSxlQUFlVSx1QkFBdUJiLE1BQWM7SUFDekQsTUFBTUksTUFBTSxJQUFJQztJQUNoQixNQUFNOEIsc0JBQXNCLElBQUk5QixLQUFLRCxJQUFJZ0IsT0FBTyxLQUFLeEI7SUFFckQsTUFBTUgsb0RBQUlBLENBQUNtQyxTQUFTLENBQ2xCO1FBQUVDLEtBQUs3QjtJQUFPLEdBQ2Q7UUFDRThCLE9BQU87WUFDTGhCLGdCQUFnQjtnQkFDZEwsWUFBWTtvQkFBRTJCLEtBQUtEO2dCQUFvQjtZQUN6QztRQUNGO0lBQ0Y7QUFFSjtBQUVBOzs7OztDQUtDLEdBQ00sZUFBZUUsZ0JBQ3BCckMsTUFBYyxFQUNkRyxZQUFvQjtJQUVwQixNQUFNVCwyREFBU0E7SUFFZixrQ0FBa0M7SUFDbEMsTUFBTW1CLHVCQUF1QmI7SUFFN0IsMERBQTBEO0lBQzFELE1BQU1VLE9BQU8sTUFBTWpCLG9EQUFJQSxDQUFDNkMsT0FBTyxDQUFDO1FBQzlCVCxLQUFLN0I7UUFDTCx3QkFBd0JHO0lBQzFCO0lBRUEsSUFBSSxDQUFDTyxNQUFNO1FBQ1QsT0FBTztJQUNUO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1pQixVQUFVakIsS0FBS0ksY0FBYyxDQUFDeUIsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFakMsS0FBSyxLQUFLSjtJQUUxRCxJQUFJLENBQUN3QixTQUFTO1FBQ1osT0FBTztJQUNUO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU12QixNQUFNLElBQUlDO0lBQ2hCLE1BQU04QixzQkFBc0IsSUFBSTlCLEtBQUtELElBQUlnQixPQUFPLEtBQUt4QjtJQUVyRCxJQUFJK0IsUUFBUWxCLFVBQVUsR0FBRzBCLHFCQUFxQjtRQUM1QyxpQ0FBaUM7UUFDakMsTUFBTU0sY0FBY3pDLFFBQVFHO1FBQzVCLE9BQU87SUFDVDtJQUVBLCtDQUErQztJQUMvQyxNQUFNVixvREFBSUEsQ0FBQ21DLFNBQVMsQ0FDbEI7UUFDRUMsS0FBSzdCO1FBQ0wsd0JBQXdCRztJQUMxQixHQUNBO1FBQ0V1QyxNQUFNO1lBQUUsK0JBQStCdEM7UUFBSTtJQUM3QztJQUdGLE9BQU87QUFDVDtBQUVBOzs7O0NBSUMsR0FDTSxlQUFlcUMsY0FDcEJ6QyxNQUFjLEVBQ2RHLFlBQW9CO0lBRXBCLE1BQU1ULDJEQUFTQTtJQUVmLE1BQU1ELG9EQUFJQSxDQUFDbUMsU0FBUyxDQUNsQjtRQUFFQyxLQUFLN0I7SUFBTyxHQUNkO1FBQ0U4QixPQUFPO1lBQUVoQixnQkFBZ0I7Z0JBQUVQLE9BQU9KO1lBQWE7UUFBRTtJQUNuRDtBQUVKO0FBRUE7Ozs7Q0FJQyxHQUNNLGVBQWV3QyxnQkFBZ0IzQyxNQUFjO0lBQ2xELE1BQU1OLDJEQUFTQTtJQUVmLE1BQU1nQixPQUFPLE1BQU1qQixvREFBSUEsQ0FBQ2tCLFFBQVEsQ0FBQ1g7SUFDakMsT0FBT1UsTUFBTUksa0JBQWtCLEVBQUU7QUFDbkM7QUFFQTs7Ozs7Q0FLQyxHQUNNLGVBQWU4Qix1QkFDcEI1QyxNQUFjLEVBQ2Q2QyxtQkFBMkI7SUFFM0IsTUFBTW5ELDJEQUFTQTtJQUVmLGtDQUFrQztJQUNsQyxNQUFNbUIsdUJBQXVCYjtJQUU3QixnREFBZ0Q7SUFDaEQsTUFBTThDLGFBQWEsTUFBTXJELG9EQUFJQSxDQUFDa0IsUUFBUSxDQUFDWDtJQUN2QyxNQUFNK0MscUJBQXFCRCxZQUFZaEMsZ0JBQWdCQyxVQUFVO0lBRWpFLDRCQUE0QjtJQUM1QixNQUFNaUMsU0FBUyxNQUFNdkQsb0RBQUlBLENBQUNtQyxTQUFTLENBQ2pDO1FBQUVDLEtBQUs3QjtJQUFPLEdBQ2Q7UUFDRThCLE9BQU87WUFBRWhCLGdCQUFnQjtnQkFBRVAsT0FBTztvQkFBRTBDLEtBQUtKO2dCQUFvQjtZQUFFO1FBQUU7SUFDbkU7SUFHRixxREFBcUQ7SUFDckQsTUFBTUssWUFBWSxNQUFNekQsb0RBQUlBLENBQUNrQixRQUFRLENBQUNYO0lBQ3RDLE1BQU1tRCxvQkFBb0JELFdBQVdwQyxnQkFBZ0JDLFVBQVU7SUFFL0Qsd0NBQXdDO0lBQ3hDLE9BQU9nQyxxQkFBcUJJO0FBQzlCO0FBRUE7OztDQUdDLEdBQ00sZUFBZUM7SUFDcEIsTUFBTTFELDJEQUFTQTtJQUVmLE1BQU1VLE1BQU0sSUFBSUM7SUFDaEIsTUFBTThCLHNCQUFzQixJQUFJOUIsS0FBS0QsSUFBSWdCLE9BQU8sS0FBS3hCO0lBRXJELHVDQUF1QztJQUN2QyxNQUFNeUQsUUFBUSxNQUFNNUQsb0RBQUlBLENBQUM4QyxJQUFJLENBQUM7UUFDNUIsNkJBQTZCO1lBQUVILEtBQUtEO1FBQW9CO0lBQzFEO0lBRUEsSUFBSW1CLGVBQWU7SUFFbkIsMENBQTBDO0lBQzFDLEtBQUssTUFBTTVDLFFBQVEyQyxNQUFPO1FBQ3hCLE1BQU1OLHFCQUFxQnJDLEtBQUtJLGNBQWMsQ0FBQ0MsTUFBTTtRQUVyRCwwQkFBMEI7UUFDMUIsTUFBTXRCLG9EQUFJQSxDQUFDbUMsU0FBUyxDQUNsQjtZQUFFQyxLQUFLbkIsS0FBS21CLEdBQUc7UUFBQyxHQUNoQjtZQUNFQyxPQUFPO2dCQUNMaEIsZ0JBQWdCO29CQUNkTCxZQUFZO3dCQUFFMkIsS0FBS0Q7b0JBQW9CO2dCQUN6QztZQUNGO1FBQ0Y7UUFHRixxREFBcUQ7UUFDckQsTUFBTW9CLGNBQWMsTUFBTTlELG9EQUFJQSxDQUFDa0IsUUFBUSxDQUFDRCxLQUFLbUIsR0FBRztRQUNoRCxNQUFNc0Isb0JBQW9CSSxhQUFhekMsZ0JBQWdCQyxVQUFVO1FBRWpFdUMsZ0JBQWlCUCxxQkFBcUJJO0lBQ3hDO0lBRUEsT0FBT0c7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvdXRpbHMvc2Vzc2lvblRva2VuLnRzPzU1YjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmFuZG9tQnl0ZXMgfSBmcm9tICdjcnlwdG8nO1xuaW1wb3J0IHsgU2Vzc2lvbkluZm8gfSBmcm9tICdAL21vZGVscy9Vc2VyJztcbmltcG9ydCBVc2VyIGZyb20gJ0AvbW9kZWxzL1VzZXInO1xuaW1wb3J0IGRiQ29ubmVjdCBmcm9tICdAL2xpYi9kYi9tb25nb2RiJztcblxuLy8gTWF4aW11bSBudW1iZXIgb2Ygc2Vzc2lvbnMgcGVyIHVzZXJcbmNvbnN0IE1BWF9TRVNTSU9OU19QRVJfVVNFUiA9IDU7XG5cbi8vIFNlc3Npb24gZXhwaXJhdGlvbiB0aW1lIGluIG1pbGxpc2Vjb25kcyAoMjQgaG91cnMpXG5jb25zdCBTRVNTSU9OX0VYUElSQVRJT05fTVMgPSAyNCAqIDYwICogNjAgKiAxMDAwO1xuXG4vKipcbiAqIEdlbmVyYXRlIGEgdW5pcXVlIHNlc3Npb24gdG9rZW5cbiAqIEByZXR1cm5zIEEgcmFuZG9tIHNlc3Npb24gdG9rZW5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlU2Vzc2lvblRva2VuKCk6IHN0cmluZyB7XG4gIHJldHVybiByYW5kb21CeXRlcygzMikudG9TdHJpbmcoJ2hleCcpO1xufVxuXG4vKipcbiAqIENyZWF0ZSBhIG5ldyBzZXNzaW9uIGZvciBhIHVzZXJcbiAqIEBwYXJhbSB1c2VySWQgVGhlIHVzZXIgSURcbiAqIEBwYXJhbSB1c2VyQWdlbnQgVGhlIHVzZXIgYWdlbnQgc3RyaW5nXG4gKiBAcGFyYW0gaXBBZGRyZXNzIFRoZSBJUCBhZGRyZXNzXG4gKiBAcmV0dXJucyBUaGUgc2Vzc2lvbiB0b2tlblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlU2Vzc2lvbihcbiAgdXNlcklkOiBzdHJpbmcsXG4gIHVzZXJBZ2VudD86IHN0cmluZyxcbiAgaXBBZGRyZXNzPzogc3RyaW5nXG4pOiBQcm9taXNlPHN0cmluZz4ge1xuICBhd2FpdCBkYkNvbm5lY3QoKTtcblxuICBjb25zdCBzZXNzaW9uVG9rZW4gPSBnZW5lcmF0ZVNlc3Npb25Ub2tlbigpO1xuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuXG4gIGNvbnN0IHNlc3Npb25JbmZvOiBTZXNzaW9uSW5mbyA9IHtcbiAgICB0b2tlbjogc2Vzc2lvblRva2VuLFxuICAgIGNyZWF0ZWRBdDogbm93LFxuICAgIGxhc3RBY3RpdmU6IG5vdyxcbiAgICB1c2VyQWdlbnQsXG4gICAgaXBBZGRyZXNzLFxuICB9O1xuXG4gIC8vIEdldCB0aGUgdXNlciB3aXRoIHRoZWlyIGN1cnJlbnQgc2Vzc2lvbnNcbiAgY29uc3QgdXNlciA9IGF3YWl0IFVzZXIuZmluZEJ5SWQodXNlcklkKTtcblxuICBpZiAoIXVzZXIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGZvdW5kJyk7XG4gIH1cblxuICAvLyBDbGVhbiB1cCBleHBpcmVkIHNlc3Npb25zIGZpcnN0XG4gIGF3YWl0IGNsZWFudXBFeHBpcmVkU2Vzc2lvbnModXNlcklkKTtcblxuICAvLyBJZiB0aGUgdXNlciBoYXMgdG9vIG1hbnkgYWN0aXZlIHNlc3Npb25zLCByZW1vdmUgdGhlIG9sZGVzdCBvbmVzXG4gIGlmICh1c2VyLmFjdGl2ZVNlc3Npb25zICYmIHVzZXIuYWN0aXZlU2Vzc2lvbnMubGVuZ3RoID49IE1BWF9TRVNTSU9OU19QRVJfVVNFUikge1xuICAgIC8vIFNvcnQgc2Vzc2lvbnMgYnkgbGFzdEFjdGl2ZSAob2xkZXN0IGZpcnN0KVxuICAgIGNvbnN0IHNvcnRlZFNlc3Npb25zID0gWy4uLnVzZXIuYWN0aXZlU2Vzc2lvbnNdLnNvcnQoXG4gICAgICAoYSwgYikgPT4gYS5sYXN0QWN0aXZlLmdldFRpbWUoKSAtIGIubGFzdEFjdGl2ZS5nZXRUaW1lKClcbiAgICApO1xuXG4gICAgLy8gQ2FsY3VsYXRlIGhvdyBtYW55IHNlc3Npb25zIHRvIHJlbW92ZVxuICAgIGNvbnN0IHNlc3Npb25zVG9SZW1vdmUgPSBNYXRoLm1heCgwLCBzb3J0ZWRTZXNzaW9ucy5sZW5ndGggLSBNQVhfU0VTU0lPTlNfUEVSX1VTRVIgKyAxKTtcblxuICAgIGlmIChzZXNzaW9uc1RvUmVtb3ZlID4gMCkge1xuICAgICAgLy8gR2V0IHRoZSB0b2tlbnMgb2YgdGhlIG9sZGVzdCBzZXNzaW9uc1xuICAgICAgY29uc3QgdG9rZW5zVG9SZW1vdmUgPSBzb3J0ZWRTZXNzaW9uc1xuICAgICAgICAuc2xpY2UoMCwgc2Vzc2lvbnNUb1JlbW92ZSlcbiAgICAgICAgLm1hcChzZXNzaW9uID0+IHNlc3Npb24udG9rZW4pO1xuXG4gICAgICAvLyBSZW1vdmUgdGhlIG9sZGVzdCBzZXNzaW9uc1xuICAgICAgYXdhaXQgVXNlci51cGRhdGVPbmUoXG4gICAgICAgIHsgX2lkOiB1c2VySWQgfSxcbiAgICAgICAgeyAkcHVsbDogeyBhY3RpdmVTZXNzaW9uczogeyB0b2tlbjogeyAkaW46IHRva2Vuc1RvUmVtb3ZlIH0gfSB9IH1cbiAgICAgICk7XG4gICAgfVxuICB9XG5cbiAgLy8gQWRkIHRoZSBuZXcgc2Vzc2lvblxuICBhd2FpdCBVc2VyLmZpbmRCeUlkQW5kVXBkYXRlKFxuICAgIHVzZXJJZCxcbiAgICB7XG4gICAgICAkcHVzaDogeyBhY3RpdmVTZXNzaW9uczogc2Vzc2lvbkluZm8gfSxcbiAgICB9LFxuICAgIHsgbmV3OiB0cnVlIH1cbiAgKTtcblxuICByZXR1cm4gc2Vzc2lvblRva2VuO1xufVxuXG4vKipcbiAqIENsZWFuIHVwIGV4cGlyZWQgc2Vzc2lvbnMgZm9yIGEgdXNlclxuICogQHBhcmFtIHVzZXJJZCBUaGUgdXNlciBJRFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2xlYW51cEV4cGlyZWRTZXNzaW9ucyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICBjb25zdCBleHBpcmF0aW9uVGhyZXNob2xkID0gbmV3IERhdGUobm93LmdldFRpbWUoKSAtIFNFU1NJT05fRVhQSVJBVElPTl9NUyk7XG5cbiAgYXdhaXQgVXNlci51cGRhdGVPbmUoXG4gICAgeyBfaWQ6IHVzZXJJZCB9LFxuICAgIHtcbiAgICAgICRwdWxsOiB7XG4gICAgICAgIGFjdGl2ZVNlc3Npb25zOiB7XG4gICAgICAgICAgbGFzdEFjdGl2ZTogeyAkbHQ6IGV4cGlyYXRpb25UaHJlc2hvbGQgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vKipcbiAqIFZhbGlkYXRlIGEgc2Vzc2lvbiB0b2tlbiBmb3IgYSB1c2VyXG4gKiBAcGFyYW0gdXNlcklkIFRoZSB1c2VyIElEXG4gKiBAcGFyYW0gc2Vzc2lvblRva2VuIFRoZSBzZXNzaW9uIHRva2VuIHRvIHZhbGlkYXRlXG4gKiBAcmV0dXJucyBUcnVlIGlmIHRoZSBzZXNzaW9uIGlzIHZhbGlkLCBmYWxzZSBvdGhlcndpc2VcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHZhbGlkYXRlU2Vzc2lvbihcbiAgdXNlcklkOiBzdHJpbmcsXG4gIHNlc3Npb25Ub2tlbjogc3RyaW5nXG4pOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgYXdhaXQgZGJDb25uZWN0KCk7XG5cbiAgLy8gQ2xlYW4gdXAgZXhwaXJlZCBzZXNzaW9ucyBmaXJzdFxuICBhd2FpdCBjbGVhbnVwRXhwaXJlZFNlc3Npb25zKHVzZXJJZCk7XG5cbiAgLy8gRmluZCB0aGUgdXNlciBhbmQgY2hlY2sgaWYgdGhleSBoYXZlIHRoaXMgc2Vzc2lvbiB0b2tlblxuICBjb25zdCB1c2VyID0gYXdhaXQgVXNlci5maW5kT25lKHtcbiAgICBfaWQ6IHVzZXJJZCxcbiAgICAnYWN0aXZlU2Vzc2lvbnMudG9rZW4nOiBzZXNzaW9uVG9rZW4sXG4gIH0pO1xuXG4gIGlmICghdXNlcikge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuXG4gIC8vIEZpbmQgdGhlIHNwZWNpZmljIHNlc3Npb25cbiAgY29uc3Qgc2Vzc2lvbiA9IHVzZXIuYWN0aXZlU2Vzc2lvbnMuZmluZChzID0+IHMudG9rZW4gPT09IHNlc3Npb25Ub2tlbik7XG5cbiAgaWYgKCFzZXNzaW9uKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgLy8gQ2hlY2sgaWYgdGhlIHNlc3Npb24gaGFzIGV4cGlyZWRcbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgY29uc3QgZXhwaXJhdGlvblRocmVzaG9sZCA9IG5ldyBEYXRlKG5vdy5nZXRUaW1lKCkgLSBTRVNTSU9OX0VYUElSQVRJT05fTVMpO1xuXG4gIGlmIChzZXNzaW9uLmxhc3RBY3RpdmUgPCBleHBpcmF0aW9uVGhyZXNob2xkKSB7XG4gICAgLy8gU2Vzc2lvbiBoYXMgZXhwaXJlZCwgcmVtb3ZlIGl0XG4gICAgYXdhaXQgcmVtb3ZlU2Vzc2lvbih1c2VySWQsIHNlc3Npb25Ub2tlbik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgLy8gVXBkYXRlIHRoZSBsYXN0IGFjdGl2ZSB0aW1lIGZvciB0aGlzIHNlc3Npb25cbiAgYXdhaXQgVXNlci51cGRhdGVPbmUoXG4gICAge1xuICAgICAgX2lkOiB1c2VySWQsXG4gICAgICAnYWN0aXZlU2Vzc2lvbnMudG9rZW4nOiBzZXNzaW9uVG9rZW4sXG4gICAgfSxcbiAgICB7XG4gICAgICAkc2V0OiB7ICdhY3RpdmVTZXNzaW9ucy4kLmxhc3RBY3RpdmUnOiBub3cgfSxcbiAgICB9XG4gICk7XG5cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8qKlxuICogUmVtb3ZlIGEgc2Vzc2lvbiBmb3IgYSB1c2VyXG4gKiBAcGFyYW0gdXNlcklkIFRoZSB1c2VyIElEXG4gKiBAcGFyYW0gc2Vzc2lvblRva2VuIFRoZSBzZXNzaW9uIHRva2VuIHRvIHJlbW92ZVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVtb3ZlU2Vzc2lvbihcbiAgdXNlcklkOiBzdHJpbmcsXG4gIHNlc3Npb25Ub2tlbjogc3RyaW5nXG4pOiBQcm9taXNlPHZvaWQ+IHtcbiAgYXdhaXQgZGJDb25uZWN0KCk7XG5cbiAgYXdhaXQgVXNlci51cGRhdGVPbmUoXG4gICAgeyBfaWQ6IHVzZXJJZCB9LFxuICAgIHtcbiAgICAgICRwdWxsOiB7IGFjdGl2ZVNlc3Npb25zOiB7IHRva2VuOiBzZXNzaW9uVG9rZW4gfSB9LFxuICAgIH1cbiAgKTtcbn1cblxuLyoqXG4gKiBHZXQgYWxsIGFjdGl2ZSBzZXNzaW9ucyBmb3IgYSB1c2VyXG4gKiBAcGFyYW0gdXNlcklkIFRoZSB1c2VyIElEXG4gKiBAcmV0dXJucyBBcnJheSBvZiBhY3RpdmUgc2Vzc2lvbnNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJTZXNzaW9ucyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8U2Vzc2lvbkluZm9bXT4ge1xuICBhd2FpdCBkYkNvbm5lY3QoKTtcblxuICBjb25zdCB1c2VyID0gYXdhaXQgVXNlci5maW5kQnlJZCh1c2VySWQpO1xuICByZXR1cm4gdXNlcj8uYWN0aXZlU2Vzc2lvbnMgfHwgW107XG59XG5cbi8qKlxuICogUmVtb3ZlIGFsbCBzZXNzaW9ucyBmb3IgYSB1c2VyIGV4Y2VwdCB0aGUgY3VycmVudCBvbmVcbiAqIEBwYXJhbSB1c2VySWQgVGhlIHVzZXIgSURcbiAqIEBwYXJhbSBjdXJyZW50U2Vzc2lvblRva2VuIFRoZSBjdXJyZW50IHNlc3Npb24gdG9rZW4gdG8ga2VlcFxuICogQHJldHVybnMgVGhlIG51bWJlciBvZiBzZXNzaW9ucyByZW1vdmVkXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZW1vdmVBbGxPdGhlclNlc3Npb25zKFxuICB1c2VySWQ6IHN0cmluZyxcbiAgY3VycmVudFNlc3Npb25Ub2tlbjogc3RyaW5nXG4pOiBQcm9taXNlPG51bWJlcj4ge1xuICBhd2FpdCBkYkNvbm5lY3QoKTtcblxuICAvLyBGaXJzdCBjbGVhbiB1cCBleHBpcmVkIHNlc3Npb25zXG4gIGF3YWl0IGNsZWFudXBFeHBpcmVkU2Vzc2lvbnModXNlcklkKTtcblxuICAvLyBHZXQgdGhlIHVzZXIgdG8gY291bnQgc2Vzc2lvbnMgYmVmb3JlIHJlbW92YWxcbiAgY29uc3QgdXNlckJlZm9yZSA9IGF3YWl0IFVzZXIuZmluZEJ5SWQodXNlcklkKTtcbiAgY29uc3Qgc2Vzc2lvbkNvdW50QmVmb3JlID0gdXNlckJlZm9yZT8uYWN0aXZlU2Vzc2lvbnM/Lmxlbmd0aCB8fCAwO1xuXG4gIC8vIFJlbW92ZSBhbGwgb3RoZXIgc2Vzc2lvbnNcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVXNlci51cGRhdGVPbmUoXG4gICAgeyBfaWQ6IHVzZXJJZCB9LFxuICAgIHtcbiAgICAgICRwdWxsOiB7IGFjdGl2ZVNlc3Npb25zOiB7IHRva2VuOiB7ICRuZTogY3VycmVudFNlc3Npb25Ub2tlbiB9IH0gfSxcbiAgICB9XG4gICk7XG5cbiAgLy8gR2V0IHRoZSB1c2VyIGFnYWluIHRvIGNvdW50IHNlc3Npb25zIGFmdGVyIHJlbW92YWxcbiAgY29uc3QgdXNlckFmdGVyID0gYXdhaXQgVXNlci5maW5kQnlJZCh1c2VySWQpO1xuICBjb25zdCBzZXNzaW9uQ291bnRBZnRlciA9IHVzZXJBZnRlcj8uYWN0aXZlU2Vzc2lvbnM/Lmxlbmd0aCB8fCAwO1xuXG4gIC8vIFJldHVybiB0aGUgbnVtYmVyIG9mIHNlc3Npb25zIHJlbW92ZWRcbiAgcmV0dXJuIHNlc3Npb25Db3VudEJlZm9yZSAtIHNlc3Npb25Db3VudEFmdGVyO1xufVxuXG4vKipcbiAqIENsZWFudXAgYWxsIGV4cGlyZWQgc2Vzc2lvbnMgaW4gdGhlIGRhdGFiYXNlXG4gKiBUaGlzIGNhbiBiZSBydW4gYXMgYSBzY2hlZHVsZWQgdGFza1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2xlYW51cEFsbEV4cGlyZWRTZXNzaW9ucygpOiBQcm9taXNlPG51bWJlcj4ge1xuICBhd2FpdCBkYkNvbm5lY3QoKTtcblxuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICBjb25zdCBleHBpcmF0aW9uVGhyZXNob2xkID0gbmV3IERhdGUobm93LmdldFRpbWUoKSAtIFNFU1NJT05fRVhQSVJBVElPTl9NUyk7XG5cbiAgLy8gRmluZCBhbGwgdXNlcnMgd2l0aCBleHBpcmVkIHNlc3Npb25zXG4gIGNvbnN0IHVzZXJzID0gYXdhaXQgVXNlci5maW5kKHtcbiAgICAnYWN0aXZlU2Vzc2lvbnMubGFzdEFjdGl2ZSc6IHsgJGx0OiBleHBpcmF0aW9uVGhyZXNob2xkIH1cbiAgfSk7XG5cbiAgbGV0IHRvdGFsUmVtb3ZlZCA9IDA7XG5cbiAgLy8gQ2xlYW4gdXAgZXhwaXJlZCBzZXNzaW9ucyBmb3IgZWFjaCB1c2VyXG4gIGZvciAoY29uc3QgdXNlciBvZiB1c2Vycykge1xuICAgIGNvbnN0IHNlc3Npb25Db3VudEJlZm9yZSA9IHVzZXIuYWN0aXZlU2Vzc2lvbnMubGVuZ3RoO1xuXG4gICAgLy8gUmVtb3ZlIGV4cGlyZWQgc2Vzc2lvbnNcbiAgICBhd2FpdCBVc2VyLnVwZGF0ZU9uZShcbiAgICAgIHsgX2lkOiB1c2VyLl9pZCB9LFxuICAgICAge1xuICAgICAgICAkcHVsbDoge1xuICAgICAgICAgIGFjdGl2ZVNlc3Npb25zOiB7XG4gICAgICAgICAgICBsYXN0QWN0aXZlOiB7ICRsdDogZXhwaXJhdGlvblRocmVzaG9sZCB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIEdldCB0aGUgdXNlciBhZ2FpbiB0byBjb3VudCBzZXNzaW9ucyBhZnRlciByZW1vdmFsXG4gICAgY29uc3QgdXBkYXRlZFVzZXIgPSBhd2FpdCBVc2VyLmZpbmRCeUlkKHVzZXIuX2lkKTtcbiAgICBjb25zdCBzZXNzaW9uQ291bnRBZnRlciA9IHVwZGF0ZWRVc2VyPy5hY3RpdmVTZXNzaW9ucz8ubGVuZ3RoIHx8IDA7XG5cbiAgICB0b3RhbFJlbW92ZWQgKz0gKHNlc3Npb25Db3VudEJlZm9yZSAtIHNlc3Npb25Db3VudEFmdGVyKTtcbiAgfVxuXG4gIHJldHVybiB0b3RhbFJlbW92ZWQ7XG59XG4iXSwibmFtZXMiOlsicmFuZG9tQnl0ZXMiLCJVc2VyIiwiZGJDb25uZWN0IiwiTUFYX1NFU1NJT05TX1BFUl9VU0VSIiwiU0VTU0lPTl9FWFBJUkFUSU9OX01TIiwiZ2VuZXJhdGVTZXNzaW9uVG9rZW4iLCJ0b1N0cmluZyIsImNyZWF0ZVNlc3Npb24iLCJ1c2VySWQiLCJ1c2VyQWdlbnQiLCJpcEFkZHJlc3MiLCJzZXNzaW9uVG9rZW4iLCJub3ciLCJEYXRlIiwic2Vzc2lvbkluZm8iLCJ0b2tlbiIsImNyZWF0ZWRBdCIsImxhc3RBY3RpdmUiLCJ1c2VyIiwiZmluZEJ5SWQiLCJFcnJvciIsImNsZWFudXBFeHBpcmVkU2Vzc2lvbnMiLCJhY3RpdmVTZXNzaW9ucyIsImxlbmd0aCIsInNvcnRlZFNlc3Npb25zIiwic29ydCIsImEiLCJiIiwiZ2V0VGltZSIsInNlc3Npb25zVG9SZW1vdmUiLCJNYXRoIiwibWF4IiwidG9rZW5zVG9SZW1vdmUiLCJzbGljZSIsIm1hcCIsInNlc3Npb24iLCJ1cGRhdGVPbmUiLCJfaWQiLCIkcHVsbCIsIiRpbiIsImZpbmRCeUlkQW5kVXBkYXRlIiwiJHB1c2giLCJuZXciLCJleHBpcmF0aW9uVGhyZXNob2xkIiwiJGx0IiwidmFsaWRhdGVTZXNzaW9uIiwiZmluZE9uZSIsImZpbmQiLCJzIiwicmVtb3ZlU2Vzc2lvbiIsIiRzZXQiLCJnZXRVc2VyU2Vzc2lvbnMiLCJyZW1vdmVBbGxPdGhlclNlc3Npb25zIiwiY3VycmVudFNlc3Npb25Ub2tlbiIsInVzZXJCZWZvcmUiLCJzZXNzaW9uQ291bnRCZWZvcmUiLCJyZXN1bHQiLCIkbmUiLCJ1c2VyQWZ0ZXIiLCJzZXNzaW9uQ291bnRBZnRlciIsImNsZWFudXBBbGxFeHBpcmVkU2Vzc2lvbnMiLCJ1c2VycyIsInRvdGFsUmVtb3ZlZCIsInVwZGF0ZWRVc2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/sessionToken.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CEuf%5CCoding%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();