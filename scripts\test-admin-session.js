/**
 * <PERSON><PERSON>t to test admin session and authentication
 * This will help debug the admin login issues
 */

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

// Define the User schema
const UserSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a name'],
      maxlength: [60, 'Name cannot be more than 60 characters'],
      unique: true,
      trim: true,
    },
    email: {
      type: String,
      required: false,
      lowercase: true,
      trim: true,
    },
    password: {
      type: String,
      select: false, // Don't return password by default
    },
    role: {
      type: String,
      enum: ['ADMIN', 'REGIONAL_DIRECTOR', 'DIVISION_CHIEF', 'EMPLOYEE'],
      default: 'EMPLOYEE',
    },
    division: {
      type: String,
      enum: ['ORD', 'FAD', 'MMD', 'MSESDD', 'GSD'],
      required: [true, 'Please provide a division'],
    },
    image: {
      type: String,
    },
    activeSessions: {
      type: Array,
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

// Create the User model
let User;
try {
  User = mongoose.model('User');
} catch (error) {
  User = mongoose.model('User', UserSchema);
}

async function testAdminSession() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find all admin users
    const adminUsers = await User.find({ 
      role: { $in: ['ADMIN', 'REGIONAL_DIRECTOR'] } 
    }).select('+password');

    console.log('\n=== ADMIN USERS FOUND ===');
    for (const user of adminUsers) {
      console.log(`ID: ${user._id}`);
      console.log(`Name: ${user.name}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Division: ${user.division}`);
      console.log(`Has Password: ${!!user.password}`);
      console.log(`Created: ${user.createdAt}`);
      console.log(`Updated: ${user.updatedAt}`);
      console.log('---');
    }

    // Test password verification for each admin
    console.log('\n=== PASSWORD VERIFICATION TEST ===');
    for (const user of adminUsers) {
      if (user.password) {
        try {
          // Test common passwords
          const testPasswords = ['password123', 'Admin123!', 'admin', user.name];
          
          for (const testPassword of testPasswords) {
            const isValid = await bcrypt.compare(testPassword, user.password);
            if (isValid) {
              console.log(`✅ User "${user.name}" password is: "${testPassword}"`);
              break;
            }
          }
        } catch (error) {
          console.log(`❌ Error testing password for user "${user.name}":`, error.message);
        }
      } else {
        console.log(`❌ User "${user.name}" has no password set`);
      }
    }

    // Check if there are any issues with the user structure
    console.log('\n=== USER STRUCTURE VALIDATION ===');
    for (const user of adminUsers) {
      const issues = [];
      
      if (!user._id) issues.push('Missing _id');
      if (!user.name) issues.push('Missing name');
      if (!user.role) issues.push('Missing role');
      if (!user.division) issues.push('Missing division');
      if (!user.password) issues.push('Missing password');
      
      if (issues.length > 0) {
        console.log(`❌ User "${user.name}" has issues: ${issues.join(', ')}`);
      } else {
        console.log(`✅ User "${user.name}" structure is valid`);
      }
    }

    console.log('\n=== SUMMARY ===');
    console.log(`Total admin users found: ${adminUsers.length}`);
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found! You need to create an admin user first.');
      console.log('Run: node scripts/create-admin-user.js admin Admin123! ORD');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

testAdminSession();
