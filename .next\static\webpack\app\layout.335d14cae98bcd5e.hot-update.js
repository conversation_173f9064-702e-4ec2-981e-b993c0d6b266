"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: function() { return /* binding */ SettingsProvider; },\n/* harmony export */   useSettings: function() { return /* binding */ useSettings; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider auto */ \n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nvar defaultSettings = {\n    colorScheme: \"default\",\n    darkMode: false,\n    fontSize: \"medium\"\n};\nvar SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    updateSettings: /*#__PURE__*/ (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n            return [\n                2\n            ];\n        });\n    }),\n    isLoading: false,\n    error: null\n});\nvar useSettings = function() {\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n};\n_s(useSettings, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar SettingsProvider = function(param) {\n    var children = param.children;\n    _s1();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)(), session = _useSession.data, status = _useSession.status;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings), 2), settings = _useState[0], setSettings = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), isLoading = _useState1[0], setIsLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), error = _useState2[0], setError = _useState2[1];\n    // Fetch settings on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var fetchSettings = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n                var response, data, err;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            _state.trys.push([\n                                0,\n                                3,\n                                4,\n                                5\n                            ]);\n                            setIsLoading(true);\n                            setError(null);\n                            return [\n                                4,\n                                fetch(\"/api/settings\")\n                            ];\n                        case 1:\n                            response = _state.sent();\n                            if (!response.ok) {\n                                // If unauthorized (401), just use default settings instead of throwing error\n                                if (response.status === 401) {\n                                    console.warn(\"User not authenticated, using default settings\");\n                                    setSettings(defaultSettings);\n                                    return [\n                                        2\n                                    ];\n                                }\n                                throw new Error(\"Failed to fetch settings\");\n                            }\n                            return [\n                                4,\n                                response.json()\n                            ];\n                        case 2:\n                            data = _state.sent();\n                            if (data.success && data.settings) {\n                                setSettings({\n                                    colorScheme: data.settings.colorScheme,\n                                    darkMode: data.settings.darkMode,\n                                    fontSize: data.settings.fontSize\n                                });\n                            } else {\n                                // If no settings found, use defaults\n                                setSettings(defaultSettings);\n                            }\n                            return [\n                                3,\n                                5\n                            ];\n                        case 3:\n                            err = _state.sent();\n                            console.error(\"Error fetching settings:\", err);\n                            // Use default settings instead of showing error\n                            setSettings(defaultSettings);\n                            setError(null); // Don't show error to user, just use defaults\n                            return [\n                                3,\n                                5\n                            ];\n                        case 4:\n                            setIsLoading(false);\n                            return [\n                                7\n                            ];\n                        case 5:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function fetchSettings() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        fetchSettings();\n    }, []);\n    // Update settings\n    var updateSettings = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function(newSettings) {\n            var _document_documentElement_classList, updatedSettings, response, themeClasses, err;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            3,\n                            4\n                        ]);\n                        setIsLoading(true);\n                        setError(null);\n                        // Update local state immediately for better UX\n                        updatedSettings = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_6__._)({}, settings, newSettings);\n                        setSettings(updatedSettings);\n                        return [\n                            4,\n                            fetch(\"/api/settings\", {\n                                method: \"PUT\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify(updatedSettings)\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            // If unauthorized, just keep the local settings without throwing error\n                            if (response.status === 401) {\n                                console.warn(\"User not authenticated, settings saved locally only\");\n                                return [\n                                    2\n                                ];\n                            }\n                            throw new Error(\"Failed to update settings\");\n                        }\n                        // Apply settings in the correct order to ensure proper theme application\n                        // First, remove all theme classes regardless of what's changing\n                        themeClasses = [\n                            \"theme-default\",\n                            \"theme-lavender\",\n                            \"theme-mint\",\n                            \"theme-peach\",\n                            \"theme-skyBlue\",\n                            \"theme-coral\",\n                            \"theme-lemon\",\n                            \"theme-periwinkle\",\n                            \"theme-rose\"\n                        ];\n                        (_document_documentElement_classList = document.documentElement.classList).remove.apply(_document_documentElement_classList, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_7__._)(themeClasses));\n                        // Special case: if selecting default color scheme, ensure dark mode is off\n                        if (newSettings.colorScheme === \"default\") {\n                            document.documentElement.classList.remove(\"dark\");\n                            // Update the darkMode setting to false if it's not already\n                            if (newSettings.darkMode === true) {\n                                newSettings.darkMode = false;\n                                // Also update localStorage to keep it in sync\n                                localStorage.setItem(\"darkMode\", \"false\");\n                            }\n                        } else if (newSettings.darkMode !== undefined) {\n                            if (newSettings.darkMode) {\n                                document.documentElement.classList.add(\"dark\");\n                            } else {\n                                document.documentElement.classList.remove(\"dark\");\n                            }\n                        }\n                        // Finally apply color scheme (after dark mode)\n                        // This ensures the color scheme's variables are applied properly\n                        if (newSettings.colorScheme && newSettings.colorScheme !== \"default\") {\n                            document.documentElement.classList.add(\"theme-\".concat(newSettings.colorScheme));\n                        }\n                        // Apply font size\n                        if (newSettings.fontSize) {\n                            document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n                            switch(newSettings.fontSize){\n                                case \"small\":\n                                    document.documentElement.classList.add(\"text-sm\");\n                                    break;\n                                case \"medium\":\n                                    document.documentElement.classList.add(\"text-base\");\n                                    break;\n                                case \"large\":\n                                    document.documentElement.classList.add(\"text-lg\");\n                                    break;\n                            }\n                        }\n                        // Log changes\n                        if (newSettings.colorScheme || newSettings.darkMode !== undefined) {\n                            console.log(\"Applied settings - Color scheme: \".concat(newSettings.colorScheme || settings.colorScheme, \", Dark mode: \").concat(newSettings.darkMode !== undefined ? newSettings.darkMode : settings.darkMode));\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        err = _state.sent();\n                        console.error(\"Error updating settings:\", err);\n                        setError(err.message || \"An error occurred while updating settings\");\n                        // Revert to previous settings on error\n                        setSettings(settings);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateSettings(newSettings) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // Apply settings on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _document_documentElement_classList;\n        // Apply settings in the correct order to ensure proper theme application\n        // First, remove all theme classes\n        var themeClasses = [\n            \"theme-default\",\n            \"theme-lavender\",\n            \"theme-mint\",\n            \"theme-peach\",\n            \"theme-skyBlue\",\n            \"theme-coral\",\n            \"theme-lemon\",\n            \"theme-periwinkle\",\n            \"theme-rose\"\n        ];\n        (_document_documentElement_classList = document.documentElement.classList).remove.apply(_document_documentElement_classList, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_7__._)(themeClasses));\n        // Special case: if using default color scheme, ensure dark mode is off\n        if (settings.colorScheme === \"default\") {\n            document.documentElement.classList.remove(\"dark\");\n            // If settings has dark mode on but color scheme is default, update settings\n            if (settings.darkMode) {\n                setSettings(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_6__._)({}, prev), {\n                        darkMode: false\n                    });\n                });\n                // Also update localStorage to keep it in sync\n                localStorage.setItem(\"darkMode\", \"false\");\n            }\n        } else if (settings.darkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n        // Finally apply color scheme (after dark mode)\n        // This ensures the color scheme's variables are applied properly\n        if (settings.colorScheme !== \"default\") {\n            document.documentElement.classList.add(\"theme-\".concat(settings.colorScheme));\n        }\n        // Apply font size\n        document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n        switch(settings.fontSize){\n            case \"small\":\n                document.documentElement.classList.add(\"text-sm\");\n                break;\n            case \"medium\":\n                document.documentElement.classList.add(\"text-base\");\n                break;\n            case \"large\":\n                document.documentElement.classList.add(\"text-lg\");\n                break;\n        }\n        console.log(\"Applied color scheme: \".concat(settings.colorScheme, \", Dark mode: \").concat(settings.darkMode));\n    }, [\n        settings\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: {\n            settings: settings,\n            updateSettings: updateSettings,\n            isLoading: isLoading,\n            error: error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\contexts\\\\SettingsContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, _this);\n};\n_s1(SettingsProvider, \"UYbOJB1tT7qN9k/yfbT0P8qt0OU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = SettingsProvider;\nvar _c;\n$RefreshReg$(_c, \"SettingsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SettingsContext.tsx\n"));

/***/ })

});