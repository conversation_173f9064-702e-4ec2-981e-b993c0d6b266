"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: function() { return /* binding */ SettingsProvider; },\n/* harmony export */   useSettings: function() { return /* binding */ useSettings; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider auto */ \n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nvar defaultSettings = {\n    colorScheme: \"default\",\n    darkMode: false,\n    fontSize: \"medium\"\n};\nvar SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    updateSettings: /*#__PURE__*/ (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__.__generator)(this, function(_state) {\n            return [\n                2\n            ];\n        });\n    }),\n    isLoading: false,\n    error: null\n});\nvar useSettings = function() {\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n};\n_s(useSettings, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar SettingsProvider = function(param) {\n    var children = param.children;\n    _s1();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings), 2), settings = _useState[0], setSettings = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), isLoading = _useState1[0], setIsLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), error = _useState2[0], setError = _useState2[1];\n    // Fetch settings on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var fetchSettings = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n                var response, data, err;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            _state.trys.push([\n                                0,\n                                3,\n                                4,\n                                5\n                            ]);\n                            setIsLoading(true);\n                            setError(null);\n                            return [\n                                4,\n                                fetch(\"/api/settings\")\n                            ];\n                        case 1:\n                            response = _state.sent();\n                            if (!response.ok) {\n                                // If unauthorized (401), just use default settings instead of throwing error\n                                if (response.status === 401) {\n                                    console.warn(\"User not authenticated, using default settings\");\n                                    setSettings(defaultSettings);\n                                    return [\n                                        2\n                                    ];\n                                }\n                                throw new Error(\"Failed to fetch settings\");\n                            }\n                            return [\n                                4,\n                                response.json()\n                            ];\n                        case 2:\n                            data = _state.sent();\n                            if (data.success && data.settings) {\n                                setSettings({\n                                    colorScheme: data.settings.colorScheme,\n                                    darkMode: data.settings.darkMode,\n                                    fontSize: data.settings.fontSize\n                                });\n                            } else {\n                                // If no settings found, use defaults\n                                setSettings(defaultSettings);\n                            }\n                            return [\n                                3,\n                                5\n                            ];\n                        case 3:\n                            err = _state.sent();\n                            console.error(\"Error fetching settings:\", err);\n                            // Use default settings instead of showing error\n                            setSettings(defaultSettings);\n                            setError(null); // Don't show error to user, just use defaults\n                            return [\n                                3,\n                                5\n                            ];\n                        case 4:\n                            setIsLoading(false);\n                            return [\n                                7\n                            ];\n                        case 5:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function fetchSettings() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        fetchSettings();\n    }, []);\n    // Update settings\n    var updateSettings = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function(newSettings) {\n            var _document_documentElement_classList, updatedSettings, response, themeClasses, err;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            3,\n                            4\n                        ]);\n                        setIsLoading(true);\n                        setError(null);\n                        // Update local state immediately for better UX\n                        updatedSettings = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, settings, newSettings);\n                        setSettings(updatedSettings);\n                        return [\n                            4,\n                            fetch(\"/api/settings\", {\n                                method: \"PUT\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify(updatedSettings)\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            // If unauthorized, just keep the local settings without throwing error\n                            if (response.status === 401) {\n                                console.warn(\"User not authenticated, settings saved locally only\");\n                                return [\n                                    2\n                                ];\n                            }\n                            throw new Error(\"Failed to update settings\");\n                        }\n                        // Apply settings in the correct order to ensure proper theme application\n                        // First, remove all theme classes regardless of what's changing\n                        themeClasses = [\n                            \"theme-default\",\n                            \"theme-lavender\",\n                            \"theme-mint\",\n                            \"theme-peach\",\n                            \"theme-skyBlue\",\n                            \"theme-coral\",\n                            \"theme-lemon\",\n                            \"theme-periwinkle\",\n                            \"theme-rose\"\n                        ];\n                        (_document_documentElement_classList = document.documentElement.classList).remove.apply(_document_documentElement_classList, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(themeClasses));\n                        // Special case: if selecting default color scheme, ensure dark mode is off\n                        if (newSettings.colorScheme === \"default\") {\n                            document.documentElement.classList.remove(\"dark\");\n                            // Update the darkMode setting to false if it's not already\n                            if (newSettings.darkMode === true) {\n                                newSettings.darkMode = false;\n                                // Also update localStorage to keep it in sync\n                                localStorage.setItem(\"darkMode\", \"false\");\n                            }\n                        } else if (newSettings.darkMode !== undefined) {\n                            if (newSettings.darkMode) {\n                                document.documentElement.classList.add(\"dark\");\n                            } else {\n                                document.documentElement.classList.remove(\"dark\");\n                            }\n                        }\n                        // Finally apply color scheme (after dark mode)\n                        // This ensures the color scheme's variables are applied properly\n                        if (newSettings.colorScheme && newSettings.colorScheme !== \"default\") {\n                            document.documentElement.classList.add(\"theme-\".concat(newSettings.colorScheme));\n                        }\n                        // Apply font size\n                        if (newSettings.fontSize) {\n                            document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n                            switch(newSettings.fontSize){\n                                case \"small\":\n                                    document.documentElement.classList.add(\"text-sm\");\n                                    break;\n                                case \"medium\":\n                                    document.documentElement.classList.add(\"text-base\");\n                                    break;\n                                case \"large\":\n                                    document.documentElement.classList.add(\"text-lg\");\n                                    break;\n                            }\n                        }\n                        // Log changes\n                        if (newSettings.colorScheme || newSettings.darkMode !== undefined) {\n                            console.log(\"Applied settings - Color scheme: \".concat(newSettings.colorScheme || settings.colorScheme, \", Dark mode: \").concat(newSettings.darkMode !== undefined ? newSettings.darkMode : settings.darkMode));\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        err = _state.sent();\n                        console.error(\"Error updating settings:\", err);\n                        setError(err.message || \"An error occurred while updating settings\");\n                        // Revert to previous settings on error\n                        setSettings(settings);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateSettings(newSettings) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // Apply settings on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _document_documentElement_classList;\n        // Apply settings in the correct order to ensure proper theme application\n        // First, remove all theme classes\n        var themeClasses = [\n            \"theme-default\",\n            \"theme-lavender\",\n            \"theme-mint\",\n            \"theme-peach\",\n            \"theme-skyBlue\",\n            \"theme-coral\",\n            \"theme-lemon\",\n            \"theme-periwinkle\",\n            \"theme-rose\"\n        ];\n        (_document_documentElement_classList = document.documentElement.classList).remove.apply(_document_documentElement_classList, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(themeClasses));\n        // Special case: if using default color scheme, ensure dark mode is off\n        if (settings.colorScheme === \"default\") {\n            document.documentElement.classList.remove(\"dark\");\n            // If settings has dark mode on but color scheme is default, update settings\n            if (settings.darkMode) {\n                setSettings(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, prev), {\n                        darkMode: false\n                    });\n                });\n                // Also update localStorage to keep it in sync\n                localStorage.setItem(\"darkMode\", \"false\");\n            }\n        } else if (settings.darkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n        // Finally apply color scheme (after dark mode)\n        // This ensures the color scheme's variables are applied properly\n        if (settings.colorScheme !== \"default\") {\n            document.documentElement.classList.add(\"theme-\".concat(settings.colorScheme));\n        }\n        // Apply font size\n        document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n        switch(settings.fontSize){\n            case \"small\":\n                document.documentElement.classList.add(\"text-sm\");\n                break;\n            case \"medium\":\n                document.documentElement.classList.add(\"text-base\");\n                break;\n            case \"large\":\n                document.documentElement.classList.add(\"text-lg\");\n                break;\n        }\n        console.log(\"Applied color scheme: \".concat(settings.colorScheme, \", Dark mode: \").concat(settings.darkMode));\n    }, [\n        settings\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: {\n            settings: settings,\n            updateSettings: updateSettings,\n            isLoading: isLoading,\n            error: error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\contexts\\\\SettingsContext.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, _this);\n};\n_s1(SettingsProvider, \"0y+rmRwvNuJyoQfFqKa8pmSlWqU=\");\n_c = SettingsProvider;\nvar _c;\n$RefreshReg$(_c, \"SettingsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SettingsContext.tsx\n"));

/***/ })

});