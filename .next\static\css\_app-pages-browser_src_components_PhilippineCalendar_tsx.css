/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/calendar.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/* Calendar styles */
.calendar-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.react-calendar {
  width: 100%;
  border: none;
  font-family: inherit;
  background-color: transparent;
  color: inherit;
}

.react-calendar__tile {
  padding: 0.75em 0.5em;
  height: 44px;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
  color: inherit;
  text-align: center;
}

.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
  font-weight: 600;
}

.react-calendar__tile--now {
  background: rgba(26, 67, 153, 0.1);
}

.react-calendar__tile--active {
  background: rgb(26, 67, 153);
  color: white;
}

.react-calendar__navigation {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
}

.react-calendar__navigation button {
  min-width: 44px;
  height: 44px;
  border-radius: 0.5rem;
}

.react-calendar__navigation button:enabled:hover,
.react-calendar__navigation button:enabled:focus {
  background-color: rgba(26, 67, 153, 0.1);
}

.react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: rgba(26, 67, 153, 0.1);
}

.react-calendar__month-view__days__day--weekend {
  color: rgb(139, 33, 49);
}

.react-calendar__month-view__days__day--neighboringMonth {
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .react-calendar__tile {
    height: 40px;
    padding: 0.5em 0.25em;
  }
}

/* Dark mode overrides */
.dark .react-calendar {
  background-color: transparent;
  color: #e5e7eb;
}

.dark .react-calendar__tile--now {
  background: rgba(26, 67, 153, 0.2);
}

.dark .react-calendar__tile--active {
  background: rgb(51, 95, 175);
  color: white;
}

.dark .react-calendar__month-view__days__day--weekend {
  color: rgb(248, 113, 113); /* red-400 */
}

.dark .react-calendar__month-view__days__day--neighboringMonth {
  color: #6b7280;
}

.dark .react-calendar__navigation button:enabled:hover,
.dark .react-calendar__navigation button:enabled:focus {
  background-color: rgba(26, 67, 153, 0.2);
}

.dark .react-calendar__tile:enabled:hover,
.dark .react-calendar__tile:enabled:focus {
  background-color: rgba(26, 67, 153, 0.2);
}

