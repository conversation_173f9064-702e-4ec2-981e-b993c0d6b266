import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Settings from '@/models/Settings';

// Mark this route as dynamic to prevent static generation
export const dynamic = 'force-dynamic';

// GET /api/settings - Get user settings
export async function GET() {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);

    console.log('Settings API - Session:', JSON.stringify(session, null, 2));

    if (!session?.user) {
      console.log('Settings API - No session or user found');
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await dbConnect();

    // Log the user ID for debugging
    console.log('Looking for settings for user ID:', session.user.id);
    console.log('User role:', session.user.role);
    console.log('User name:', session.user.name);

    // Check if user ID is valid
    if (!session.user.id) {
      console.error('User ID is missing from session:', session);
      // For admin users, we might need to handle this differently
      // Return default settings instead of error
      return NextResponse.json({
        success: true,
        settings: {
          colorScheme: 'lavender',
          darkMode: false,
          fontSize: 'medium',
        },
      });
    }

    // Find settings for the user
    let settings;
    try {
      // Try to convert to ObjectId if needed
      const mongoose = require('mongoose');
      const userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;

      settings = await Settings.findOne({ userId });

      // If settings don't exist, create default settings
      if (!settings) {
        settings = await Settings.create({
          userId: session.user.id,
          colorScheme: 'lavender', // Changed from 'blue' to 'lavender' to match the enum
          darkMode: false,
          fontSize: 'medium',
        });
      }
    } catch (error) {
      console.error('Error finding or creating settings:', error);
      return NextResponse.json(
        { message: 'Error processing settings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      settings,
    });
  } catch (error: any) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { message: error.message || 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// PUT /api/settings - Update user settings
export async function PUT(request: Request) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);

    console.log('Settings PUT API - Session:', JSON.stringify(session, null, 2));

    if (!session?.user) {
      console.log('Settings PUT API - No session or user found');
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { colorScheme, darkMode, fontSize } = body;

    // Connect to database
    await dbConnect();

    // Log the user ID for debugging
    console.log('Updating settings for user ID:', session.user.id);

    // Check if user ID is valid
    if (!session.user.id) {
      console.error('User ID is missing from session:', session);
      // For admin users, return success but don't save to database
      return NextResponse.json({
        success: true,
        settings: {
          colorScheme,
          darkMode,
          fontSize,
        },
      });
    }

    // Find and update settings for the user
    let settings;
    try {
      // Try to convert to ObjectId if needed
      const mongoose = require('mongoose');
      const userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;

      settings = await Settings.findOneAndUpdate(
        { userId },
        {
          colorScheme,
          darkMode,
          fontSize,
        },
        { new: true, upsert: true }
      );
    } catch (error) {
      console.error('Error updating settings:', error);
      return NextResponse.json(
        { message: 'Error processing settings update' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      settings,
    });
  } catch (error: any) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { message: error.message || 'Failed to update settings' },
      { status: 500 }
    );
  }
}
